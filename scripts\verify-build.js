#!/usr/bin/env node

/**
 * Build Verification Script
 * Verifies that the production build is ready for deployment
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 Verifying production build...\n');

const checks = [];

// Check if .next directory exists
if (fs.existsSync('.next')) {
  checks.push({ name: 'Build directory exists', status: '✅' });
} else {
  checks.push({ name: 'Build directory exists', status: '❌', error: 'Run npm run build first' });
}

// Check if standalone build exists
if (fs.existsSync('.next/standalone')) {
  checks.push({ name: 'Standalone build created', status: '✅' });
} else {
  checks.push({ name: 'Standalone build created', status: '⚠️', warning: 'Standalone build not found (check next.config.ts)' });
}

// Check environment files
if (fs.existsSync('.env.production')) {
  checks.push({ name: 'Production environment file exists', status: '✅' });
} else {
  checks.push({ name: 'Production environment file exists', status: '❌', error: '.env.production file missing' });
}

// Check package.json scripts
const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
const requiredScripts = ['build', 'start', 'build:production', 'start:production'];
const missingScripts = requiredScripts.filter(script => !packageJson.scripts[script]);

if (missingScripts.length === 0) {
  checks.push({ name: 'Required scripts present', status: '✅' });
} else {
  checks.push({ name: 'Required scripts present', status: '❌', error: `Missing scripts: ${missingScripts.join(', ')}` });
}

// Check TypeScript configuration
if (fs.existsSync('tsconfig.json')) {
  checks.push({ name: 'TypeScript configuration exists', status: '✅' });
} else {
  checks.push({ name: 'TypeScript configuration exists', status: '❌', error: 'tsconfig.json missing' });
}

// Check Next.js configuration
if (fs.existsSync('next.config.ts') || fs.existsSync('next.config.js')) {
  checks.push({ name: 'Next.js configuration exists', status: '✅' });
} else {
  checks.push({ name: 'Next.js configuration exists', status: '❌', error: 'next.config.ts/js missing' });
}

// Display results
console.log('Build Verification Results:');
console.log('==========================\n');

let hasErrors = false;
let hasWarnings = false;

checks.forEach(check => {
  console.log(`${check.status} ${check.name}`);
  if (check.error) {
    console.log(`   Error: ${check.error}`);
    hasErrors = true;
  }
  if (check.warning) {
    console.log(`   Warning: ${check.warning}`);
    hasWarnings = true;
  }
});

console.log('\n==========================');

if (hasErrors) {
  console.log('❌ Build verification failed. Please fix the errors above.');
  process.exit(1);
} else if (hasWarnings) {
  console.log('⚠️  Build verification completed with warnings.');
  process.exit(0);
} else {
  console.log('✅ Build verification passed! Ready for deployment.');
  process.exit(0);
}
