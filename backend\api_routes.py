"""
REST API routes for the WebSocket-based VPS Admin backend.
Handles authentication, task management, and system operations.
"""

from datetime import datetime
from typing import List, Optional
from fastapi import APIRouter, HTTPException, Depends, status
from fastapi.responses import JSONResponse
import structlog

from models import (
    CreateTaskRequest, CreateTaskResponse, Task, TaskStatus,
    UserCreate, UserLogin, Token, User, APIResponse, ErrorResponse,
    HealthCheck, UserContext
)
from auth import auth_manager, get_current_active_user, require_permission
from task_manager import task_manager
from ssh_manager import ssh_manager
from ai_client import get_ai_client
from websocket_manager import websocket_manager
from config import settings

logger = structlog.get_logger(__name__)

# Create router
router = APIRouter()

# Authentication endpoints
@router.post("/auth/register", response_model=User, tags=["Authentication"])
async def register_user(user_data: UserCreate):
    """Register a new user."""
    try:
        user = auth_manager.create_user(user_data)
        logger.info("User registered", username=user.username, user_id=user.id)
        return user
    except Exception as e:
        logger.error("User registration failed", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )

@router.post("/auth/login", response_model=Token, tags=["Authentication"])
async def login_user(login_data: UserLogin):
    """Login user and return access token."""
    try:
        token = auth_manager.login(login_data)
        logger.info("User logged in", username=login_data.username)
        return token
    except Exception as e:
        logger.error("Login failed", username=login_data.username, error=str(e))
        raise e

@router.get("/auth/me", response_model=dict, tags=["Authentication"])
async def get_current_user_info(current_user: UserContext = Depends(get_current_active_user)):
    """Get current user information."""
    return {
        "user_id": current_user.user_id,
        "permissions": current_user.permissions
    }

# Task management endpoints
@router.post("/tasks", response_model=CreateTaskResponse, tags=["Tasks"])
async def create_task(
    request: CreateTaskRequest,
    current_user: UserContext = Depends(require_permission("task:create"))
):
    """Create a new task."""
    try:
        response = await task_manager.create_task(request, current_user)
        logger.info("Task created via API", 
                   task_id=response.task_id, 
                   user_id=current_user.user_id)
        return response
    except Exception as e:
        logger.error("Task creation failed", error=str(e), user_id=current_user.user_id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create task: {str(e)}"
        )

@router.get("/tasks", response_model=List[Task], tags=["Tasks"])
async def get_user_tasks(
    current_user: UserContext = Depends(require_permission("task:read"))
):
    """Get all tasks for the current user."""
    try:
        tasks = await task_manager.get_user_tasks(current_user)
        return tasks
    except Exception as e:
        logger.error("Failed to get user tasks", error=str(e), user_id=current_user.user_id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get tasks: {str(e)}"
        )

@router.get("/tasks/{task_id}", response_model=Task, tags=["Tasks"])
async def get_task(
    task_id: str,
    current_user: UserContext = Depends(require_permission("task:read"))
):
    """Get a specific task by ID."""
    task = await task_manager.get_task(task_id, current_user)
    if not task:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Task not found"
        )
    return task

@router.delete("/tasks/{task_id}", response_model=APIResponse, tags=["Tasks"])
async def delete_task(
    task_id: str,
    current_user: UserContext = Depends(require_permission("task:delete"))
):
    """Delete a task."""
    success = await task_manager.delete_task(task_id, current_user)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Task not found or access denied"
        )
    
    logger.info("Task deleted via API", task_id=task_id, user_id=current_user.user_id)
    return APIResponse(
        success=True,
        message=f"Task {task_id} deleted successfully"
    )

@router.put("/tasks/{task_id}/status", response_model=APIResponse, tags=["Tasks"])
async def update_task_status(
    task_id: str,
    status_update: dict,
    current_user: UserContext = Depends(require_permission("task:update"))
):
    """Update task status."""
    try:
        new_status = TaskStatus(status_update.get("status"))
        error_message = status_update.get("error_message")
        
        success = await task_manager.update_task_status(task_id, new_status, error_message)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Task not found"
            )
        
        return APIResponse(
            success=True,
            message=f"Task status updated to {new_status.value}"
        )
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid status: {str(e)}"
        )
    except Exception as e:
        logger.error("Failed to update task status", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update task status: {str(e)}"
        )

# System endpoints
@router.get("/system/info", response_model=dict, tags=["System"])
async def get_system_info(
    current_user: UserContext = Depends(require_permission("system:read"))
):
    """Get system information."""
    try:
        context, os_type = await ssh_manager.gather_system_context(current_user)
        return {
            "context": context,
            "os_type": os_type,
            "timestamp": datetime.utcnow().isoformat()
        }
    except Exception as e:
        logger.error("Failed to get system info", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get system info: {str(e)}"
        )

@router.post("/system/test-connection", response_model=APIResponse, tags=["System"])
async def test_ssh_connection(
    current_user: UserContext = Depends(require_permission("system:read"))
):
    """Test SSH connection."""
    try:
        success = await ssh_manager.test_connection(current_user)
        return APIResponse(
            success=success,
            message="SSH connection successful" if success else "SSH connection failed"
        )
    except Exception as e:
        logger.error("SSH connection test failed", error=str(e))
        return APIResponse(
            success=False,
            message=f"SSH connection test failed: {str(e)}"
        )

# Health and monitoring endpoints
@router.get("/health", response_model=HealthCheck, tags=["Health"])
async def health_check():
    """Health check endpoint."""
    try:
        # Get statistics
        ws_stats = await websocket_manager.get_connection_stats()
        task_stats = await task_manager.get_stats()
        ssh_stats = await ssh_manager.get_connection_stats()
        
        return HealthCheck(
            status="healthy",
            version=settings.app_version,
            uptime=0.0,  # TODO: Calculate actual uptime
            active_connections=ws_stats["total_connections"],
            active_tasks=task_stats["total_tasks"],
            system_info={
                "websocket_stats": ws_stats,
                "task_stats": task_stats,
                "ssh_stats": ssh_stats
            }
        )
    except Exception as e:
        logger.error("Health check failed", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Health check failed"
        )

@router.get("/stats", response_model=dict, tags=["Monitoring"])
async def get_system_stats(
    current_user: UserContext = Depends(require_permission("admin"))
):
    """Get detailed system statistics (admin only)."""
    try:
        ws_stats = await websocket_manager.get_connection_stats()
        task_stats = await task_manager.get_stats()
        ssh_stats = await ssh_manager.get_connection_stats()
        ai_stats = await get_ai_client().get_cache_stats()
        
        return {
            "websocket": ws_stats,
            "tasks": task_stats,
            "ssh": ssh_stats,
            "ai": ai_stats,
            "timestamp": datetime.utcnow().isoformat()
        }
    except Exception as e:
        logger.error("Failed to get system stats", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get system stats: {str(e)}"
        )

# Root endpoint
@router.get("/", response_model=dict, tags=["Root"])
async def read_root():
    """Root endpoint."""
    return {
        "status": "ok",
        "service": settings.app_name,
        "version": settings.app_version,
        "timestamp": datetime.utcnow().isoformat()
    }
