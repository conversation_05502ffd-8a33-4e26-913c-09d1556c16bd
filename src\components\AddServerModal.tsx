/**
 * AddServerModal component for adding new servers
 * Mobile-friendly and theme-aware modal component
 */

import React, { useState } from 'react';
import { Server, Plus, X } from 'lucide-react';
import { motion } from 'framer-motion';

interface AddServerModalProps {
  isOpen: boolean;
  onClose: () => void;
  onAdd: (serverData: { name: string; host: string; port: number; username: string }) => void;
}

const AddServerModal: React.FC<AddServerModalProps> = ({
  isOpen,
  onClose,
  onAdd
}) => {
  const [formData, setFormData] = useState({
    name: '',
    host: '',
    port: 22,
    username: 'root'
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  if (!isOpen) return null;

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Server name is required';
    }

    if (!formData.host.trim()) {
      newErrors.host = 'Host is required';
    }

    if (!formData.username.trim()) {
      newErrors.username = 'Username is required';
    }

    if (formData.port < 1 || formData.port > 65535) {
      newErrors.port = 'Port must be between 1 and 65535';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (validateForm()) {
      onAdd(formData);
      // Reset form
      setFormData({
        name: '',
        host: '',
        port: 22,
        username: 'root'
      });
      setErrors({});
      onClose();
    }
  };

  const handleInputChange = (field: string, value: string | number) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/60 backdrop-blur-sm animate-fade-in">
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.95 }}
        transition={{ duration: 0.2 }}
        className="bg-surface-primary border border-theme-primary rounded-xl shadow-2xl w-full max-w-md mx-4 overflow-hidden"
      >
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-theme-primary bg-surface-secondary">
          <div className="flex items-center gap-3">
            <Server size={20} className="text-accent-primary flex-shrink-0" />
            <h3 className="font-semibold text-theme-primary">Add New Server</h3>
          </div>
          <motion.button
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            onClick={onClose}
            className="p-1 text-theme-tertiary hover:text-theme-primary transition-colors"
          >
            <X size={18} />
          </motion.button>
        </div>

        {/* Content */}
        <form onSubmit={handleSubmit} className="p-4 space-y-4">
          {/* Server Name */}
          <div>
            <label className="block text-sm font-medium text-theme-secondary mb-1">
              Server Name
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              placeholder="e.g., Production Server"
              className={`w-full px-3 py-2 text-sm bg-surface-secondary border rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-accent-primary/50 ${
                errors.name ? 'border-accent-error' : 'border-theme-primary'
              }`}
            />
            {errors.name && (
              <p className="text-xs text-accent-error mt-1">{errors.name}</p>
            )}
          </div>

          {/* Host */}
          <div>
            <label className="block text-sm font-medium text-theme-secondary mb-1">
              Host
            </label>
            <input
              type="text"
              value={formData.host}
              onChange={(e) => handleInputChange('host', e.target.value)}
              placeholder="e.g., ************* or server.example.com"
              className={`w-full px-3 py-2 text-sm bg-surface-secondary border rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-accent-primary/50 ${
                errors.host ? 'border-accent-error' : 'border-theme-primary'
              }`}
            />
            {errors.host && (
              <p className="text-xs text-accent-error mt-1">{errors.host}</p>
            )}
          </div>

          {/* Port and Username Row */}
          <div className="grid grid-cols-2 gap-3">
            {/* Port */}
            <div>
              <label className="block text-sm font-medium text-theme-secondary mb-1">
                Port
              </label>
              <input
                type="number"
                value={formData.port}
                onChange={(e) => handleInputChange('port', parseInt(e.target.value) || 22)}
                min="1"
                max="65535"
                className={`w-full px-3 py-2 text-sm bg-surface-secondary border rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-accent-primary/50 ${
                  errors.port ? 'border-accent-error' : 'border-theme-primary'
                }`}
              />
              {errors.port && (
                <p className="text-xs text-accent-error mt-1">{errors.port}</p>
              )}
            </div>

            {/* Username */}
            <div>
              <label className="block text-sm font-medium text-theme-secondary mb-1">
                Username
              </label>
              <input
                type="text"
                value={formData.username}
                onChange={(e) => handleInputChange('username', e.target.value)}
                placeholder="root"
                className={`w-full px-3 py-2 text-sm bg-surface-secondary border rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-accent-primary/50 ${
                  errors.username ? 'border-accent-error' : 'border-theme-primary'
                }`}
              />
              {errors.username && (
                <p className="text-xs text-accent-error mt-1">{errors.username}</p>
              )}
            </div>
          </div>

          {/* Info Note */}
          <div className="bg-surface-secondary p-3 rounded-lg border border-theme-primary">
            <p className="text-xs text-theme-tertiary leading-relaxed">
              <strong>Note:</strong> Make sure the server is accessible and you have SSH access with the provided credentials.
            </p>
          </div>
        </form>

        {/* Actions */}
        <div className="flex items-center justify-end gap-2 p-4 border-t border-theme-primary bg-surface-secondary">
          <motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            type="button"
            onClick={onClose}
            className="px-4 py-2 text-sm font-medium bg-surface-primary text-theme-secondary border border-theme-primary rounded-lg hover:bg-surface-secondary transition-colors touch-manipulation"
          >
            Cancel
          </motion.button>
          <motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={handleSubmit}
            className="px-4 py-2 text-sm font-medium bg-accent-primary text-white rounded-lg hover:opacity-90 transition-all touch-manipulation flex items-center gap-2"
          >
            <Plus size={14} />
            Add Server
          </motion.button>
        </div>
      </motion.div>
    </div>
  );
};

export default AddServerModal;
