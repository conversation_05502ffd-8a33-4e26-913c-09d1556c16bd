'use client';

import { StrictMode } from 'react';
import VPSAdminChat from '@/components/VPSAdminChat';
import ThemeProvider from '@/components/ThemeProvider';
import { AuthProvider, AuthGuard } from '@/components/AuthProvider';

export default function Home() {
  return (
    <StrictMode>
      <AuthProvider>
        <ThemeProvider>
          <AuthGuard>
            <div className="h-screen bg-theme-secondary overflow-hidden transition-all duration-500">
              <VPSAdminChat />
            </div>
          </AuthGuard>
        </ThemeProvider>
      </AuthProvider>
    </StrictMode>
  );
}
