# ---- Builder Stage ----
# Installs dependencies into a virtual environment
FROM python:3.10-slim-buster AS builder
WORKDIR /usr/src/app

ENV VIRTUAL_ENV=/opt/venv
RUN python3 -m venv $VIRTUAL_ENV
ENV PATH="$VIRTUAL_ENV/bin:$PATH"

# Copy and install requirements
COPY requirements.txt ./
# Make sure your requirements.txt lists `uvicorn[standard]`
RUN pip install --no-cache-dir -r requirements.txt

# ---- Runner Stage (Final Image) ----
# The final, lean image for running the application
FROM python:3.10-slim-buster AS runner
WORKDIR /usr/src/app

# Create a non-root user for security
RUN addgroup --system flask && adduser --system --ingroup flask flask

# Copy the virtual environment from the builder stage
COPY --from=builder /opt/venv /opt/venv

# Copy the application code as the non-root user
COPY --chown=flask:flask . .

# Set the path to include the venv
ENV PATH="/opt/venv/bin:$PATH"

# Switch to the non-root user
USER flask

# Expose the port the app runs on
EXPOSE 8001

# The command to run the application with Uvicorn
# "app:app" means: from the file "app.py", use the variable "app".
# --host 0.0.0.0 makes the server accessible from outside the container.
CMD ["uvicorn", "--host", "0.0.0.0", "--port", "8001", "main:app"]