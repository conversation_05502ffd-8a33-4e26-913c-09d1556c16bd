/**
 * Theme management hook for VPS Admin Chat
 * Handles light and dark theme switching
 */

import { useEffect } from 'react';
import { useAppStore } from '@/store/appStore';

export const useTheme = () => {
  const { theme, setTheme } = useAppStore();

  // Apply theme to document root - only on client side
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const root = document.documentElement;

    // Apply theme class
    if (theme === 'dark') {
      root.classList.add('dark');
    } else {
      root.classList.remove('dark');
    }
  }, [theme]);

  // Initialize theme from localStorage on client side
  useEffect(() => {
    if (typeof window === 'undefined') return;

    try {
      const stored = localStorage.getItem('vps-admin-storage');
      if (stored) {
        const parsed = JSON.parse(stored);
        const storedTheme = parsed.state?.theme;
        if (storedTheme && storedTheme !== theme) {
          setTheme(storedTheme);
          return;
        }
      }

      // Check system preference if no stored theme
      if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches && theme === 'light') {
        setTheme('dark');
      }
    } catch (e) {
      // Ignore errors
    }
  }, []); // Run only once on mount

  const toggleTheme = () => {
    const newTheme = theme === 'light' ? 'dark' : 'light';
    setTheme(newTheme);
  };

  const getThemeIcon = () => {
    return theme === 'light' ? 'Sun' : 'Moon';
  };

  const getThemeLabel = () => {
    return theme.charAt(0).toUpperCase() + theme.slice(1);
  };

  return {
    theme,
    effectiveTheme: theme,
    setTheme,
    toggleTheme,
    getThemeIcon,
    getThemeLabel,
  };
};
