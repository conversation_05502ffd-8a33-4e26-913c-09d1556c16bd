/**
 * React hook for WebSocket communication
 * Provides a clean interface for WebSocket operations in React components
 */

import { useEffect, useRef, useState, useCallback } from 'react';
import { websocketService, WebSocketMessage } from '@/services/websocketService';

export interface UseWebSocketOptions {
  token?: string;
  autoConnect?: boolean;
  onMessage?: (message: WebSocketMessage) => void;
  onError?: (error: Error) => void;
  onConnect?: () => void;
  onDisconnect?: () => void;
}

export interface UseWebSocketReturn {
  isConnected: boolean;
  connectionState: string;
  sendTaskMessage: (taskId: string, message: string) => void;
  subscribeToTask: (taskId: string) => void;
  unsubscribeFromTask: (taskId: string) => void;
  error: Error | null;
}

export function useWebSocket(options: UseWebSocketOptions = {}): UseWebSocketReturn {
  const {
    token,
    autoConnect = false,
    onMessage,
    onError,
    onConnect,
    onDisconnect
  } = options;

  const [isConnected, setIsConnected] = useState(false);
  const [connectionState, setConnectionState] = useState('DISCONNECTED');
  const [error, setError] = useState<Error | null>(null);
  
  // Use refs to store the latest callback functions
  const onMessageRef = useRef(onMessage);
  const onErrorRef = useRef(onError);
  const onConnectRef = useRef(onConnect);
  const onDisconnectRef = useRef(onDisconnect);

  // Update refs when callbacks change
  useEffect(() => {
    onMessageRef.current = onMessage;
  }, [onMessage]);

  useEffect(() => {
    onErrorRef.current = onError;
  }, [onError]);

  useEffect(() => {
    onConnectRef.current = onConnect;
  }, [onConnect]);

  useEffect(() => {
    onDisconnectRef.current = onDisconnect;
  }, [onDisconnect]);

  // Connect function
  const connect = useCallback(async (authToken: string) => {
    try {
      setError(null);
      console.log('🔗 useWebSocket: Attempting to connect...');
      await websocketService.connect(authToken);
      console.log('🔗 useWebSocket: Connection attempt completed');
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Connection failed');
      console.error('🔗 useWebSocket: Connection failed:', error);
      setError(error);
      onErrorRef.current?.(error);
      throw error;
    }
  }, []);

  // Disconnect function
  const disconnect = useCallback(() => {
    websocketService.disconnect();
  }, []);

  // WebSocket command functions
  const sendTaskMessage = useCallback((taskId: string, message: string) => {
    try {
      websocketService.sendTaskMessage(taskId, message);
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to send message');
      setError(error);
      onErrorRef.current?.(error);
    }
  }, []);

  const subscribeToTask = useCallback((taskId: string) => {
    try {
      websocketService.subscribeToTask(taskId);
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to subscribe to task');
      setError(error);
      onErrorRef.current?.(error);
    }
  }, []);

  const unsubscribeFromTask = useCallback((taskId: string) => {
    try {
      websocketService.unsubscribeFromTask(taskId);
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to unsubscribe from task');
      setError(error);
      onErrorRef.current?.(error);
    }
  }, []);

  // Set up WebSocket event listeners
  useEffect(() => {
    // Message handler
    const unsubscribeMessage = websocketService.onMessage((message) => {
      onMessageRef.current?.(message);
    });

    // Error handler
    const unsubscribeError = websocketService.onError((error) => {
      setError(error);
      onErrorRef.current?.(error);
    });

    // Connect handler
    const unsubscribeConnect = websocketService.onConnect(() => {
      setIsConnected(true);
      setConnectionState('CONNECTED');
      setError(null);
      onConnectRef.current?.();
    });

    // Disconnect handler
    const unsubscribeDisconnect = websocketService.onDisconnect(() => {
      setIsConnected(false);
      setConnectionState('DISCONNECTED');
      onDisconnectRef.current?.();
    });

    // Update connection state periodically
    const stateInterval = setInterval(() => {
      const state = websocketService.getConnectionState();
      setConnectionState(state);
      setIsConnected(websocketService.isConnected());
    }, 1000);

    // Cleanup function
    return () => {
      unsubscribeMessage();
      unsubscribeError();
      unsubscribeConnect();
      unsubscribeDisconnect();
      clearInterval(stateInterval);
    };
  }, []);

  // Auto-connect effect
  useEffect(() => {
    if (autoConnect && token && !isConnected && connectionState === 'DISCONNECTED') {
      // Add a small delay to ensure auth context is fully loaded
      const timer = setTimeout(() => {
        // Double-check token is still valid before connecting
        if (token && token !== 'demo-token') {
          console.log('🔗 useWebSocket: Auto-connecting with authenticated token...');
          connect(token).catch((err) => {
            console.error('🔗 useWebSocket: Auto-connect failed:', err);
          });
        }
      }, 1000); // 1 second delay to allow auth to complete

      return () => clearTimeout(timer);
    }
  }, [autoConnect, token, isConnected, connectionState, connect]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      websocketService.disconnect();
    };
  }, []);

  return {
    isConnected,
    connectionState,
    sendTaskMessage,
    subscribeToTask,
    unsubscribeFromTask,
    error
  };
}

export default useWebSocket;
