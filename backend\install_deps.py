#!/usr/bin/env python3
"""
Dependency installation script for VPS Admin WebSocket Backend.
Handles version conflicts and ensures compatible package versions.
"""

import subprocess
import sys
import os
from pathlib import Path

def run_command(command, description=""):
    """Run a command and handle errors."""
    print(f"Running: {command}")
    if description:
        print(f"Description: {description}")
    
    try:
        result = subprocess.run(
            command.split(),
            capture_output=True,
            text=True,
            check=True
        )
        print("✅ Success")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error: {e}")
        print(f"stdout: {e.stdout}")
        print(f"stderr: {e.stderr}")
        return False

def check_python_version():
    """Check Python version compatibility."""
    version = sys.version_info
    if version < (3, 8):
        print(f"❌ Python {version.major}.{version.minor} is not supported. Please use Python 3.8+")
        return False
    
    print(f"✅ Python {version.major}.{version.minor}.{version.micro} is compatible")
    return True

def upgrade_pip():
    """Upgrade pip to latest version."""
    print("\n📦 Upgrading pip...")
    return run_command(
        f"{sys.executable} -m pip install --upgrade pip",
        "Upgrading pip to latest version"
    )

def install_core_dependencies():
    """Install core dependencies first to resolve conflicts."""
    print("\n📦 Installing core dependencies...")
    
    # Install core packages that other packages depend on
    core_packages = [
        "anyio>=4.8.0,<5.0.0",
        "pydantic>=2.7.0",
        "pydantic-settings>=2.0.0",
        "websockets>=13.0.0,<15.1.0"
    ]
    
    for package in core_packages:
        success = run_command(
            f"{sys.executable} -m pip install '{package}'",
            f"Installing {package}"
        )
        if not success:
            return False
    
    return True

def install_remaining_dependencies():
    """Install remaining dependencies."""
    print("\n📦 Installing remaining dependencies...")
    
    remaining_packages = [
        "fastapi==0.104.1",
        "uvicorn[standard]==0.24.0",
        "python-jose[cryptography]==3.3.0",
        "passlib[bcrypt]==1.7.4",
        "python-multipart==0.0.6",
        "google-generativeai>=0.8.5",
        "paramiko>=3.4.0",
        "python-dotenv>=1.0.0",
        "jsonschema>=4.20.0",
        "redis>=5.0.1",
        "aiofiles>=23.2.1",
        "asyncio-throttle>=1.0.2",
        "structlog>=23.2.0"
    ]
    
    for package in remaining_packages:
        success = run_command(
            f"{sys.executable} -m pip install '{package}'",
            f"Installing {package}"
        )
        if not success:
            print(f"⚠️ Warning: Failed to install {package}, continuing...")
    
    return True

def install_from_requirements():
    """Install from requirements.txt as fallback."""
    print("\n📦 Installing from requirements.txt...")
    
    if not Path("requirements.txt").exists():
        print("❌ requirements.txt not found")
        return False
    
    return run_command(
        f"{sys.executable} -m pip install -r requirements.txt",
        "Installing all dependencies from requirements.txt"
    )

def verify_installation():
    """Verify that key packages are installed correctly."""
    print("\n🔍 Verifying installation...")
    
    test_imports = [
        ("fastapi", "FastAPI"),
        ("uvicorn", "uvicorn"),
        ("pydantic", "pydantic"),
        ("pydantic_settings", "pydantic-settings"),
        ("websockets", "websockets"),
        ("google.generativeai", "google-generativeai"),
        ("paramiko", "paramiko"),
        ("structlog", "structlog")
    ]
    
    failed_imports = []
    
    for module, package_name in test_imports:
        try:
            __import__(module)
            print(f"✅ {package_name} imported successfully")
        except ImportError as e:
            print(f"❌ Failed to import {package_name}: {e}")
            failed_imports.append(package_name)
    
    if failed_imports:
        print(f"\n⚠️ Failed to import: {', '.join(failed_imports)}")
        return False
    
    print("\n✅ All key packages imported successfully!")
    return True

def clean_install():
    """Perform a clean installation by uninstalling conflicting packages first."""
    print("\n🧹 Performing clean installation...")
    
    # Packages that commonly cause conflicts
    conflicting_packages = [
        "sse-starlette",  # Has anyio version conflict
        "google-genai",   # Different from google-generativeai
        "anyio",
        "pydantic",
        "websockets"
    ]
    
    print("Uninstalling potentially conflicting packages...")
    for package in conflicting_packages:
        run_command(
            f"{sys.executable} -m pip uninstall -y {package}",
            f"Uninstalling {package}"
        )
    
    # Now install fresh
    return install_core_dependencies() and install_remaining_dependencies()

def main():
    """Main installation function."""
    print("🚀 VPS Admin WebSocket Backend - Dependency Installation")
    print("=" * 60)
    
    # Change to script directory
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Upgrade pip
    if not upgrade_pip():
        print("⚠️ Warning: Failed to upgrade pip, continuing...")
    
    # Try normal installation first
    print("\n🎯 Attempting normal installation...")
    if install_from_requirements():
        if verify_installation():
            print("\n🎉 Installation completed successfully!")
            return
        else:
            print("\n⚠️ Installation completed but some imports failed.")
    
    # If normal installation fails, try clean install
    print("\n🔄 Normal installation had issues, trying clean installation...")
    if clean_install():
        if verify_installation():
            print("\n🎉 Clean installation completed successfully!")
            return
        else:
            print("\n⚠️ Clean installation completed but some imports failed.")
    
    print("\n❌ Installation failed. Please check the errors above.")
    print("\nTroubleshooting tips:")
    print("1. Make sure you're using Python 3.8+")
    print("2. Try creating a fresh virtual environment")
    print("3. Update pip: python -m pip install --upgrade pip")
    print("4. Install packages individually if needed")
    
    sys.exit(1)

if __name__ == "__main__":
    main()
