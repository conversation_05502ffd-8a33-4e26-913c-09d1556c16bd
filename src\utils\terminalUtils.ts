/**
 * Terminal utility functions for processing and displaying terminal output
 */

/**
 * Decodes terminal output by removing ANSI escape sequences and control characters
 * @param text - Raw terminal output text
 * @returns Cleaned and decoded text suitable for display
 */
export const decodeTerminalOutput = (text: string): string => {
  if (!text) return text;

  // Enhanced terminal output processing to handle special characters better
  const cleaned = text
    // Remove ANSI escape sequences
    .replace(/\x1b\[[0-9;]*m/g, '') // Remove ANSI color codes
    .replace(/\x1b\[[0-9;]*[A-Za-z]/g, '') // Remove other ANSI escape sequences
    .replace(/\x1b\[[0-9;]*[~]/g, '') // Remove function key sequences
    .replace(/\x1b\[[\d;]*[A-Za-z]/g, '') // Remove cursor movement sequences

    // Normalize line endings
    .replace(/\r\n/g, '\n') // Convert Windows line endings
    .replace(/\r/g, '\n') // Convert remaining carriage returns

    // Handle special characters more carefully
    .replace(/\x00/g, '') // Remove null bytes
    .replace(/[\x01-\x08\x0B\x0C\x0E-\x1F]/g, '') // Remove control characters except \t and \n
    .replace(/\x7F/g, '') // Remove DEL character

    // Preserve printable ASCII and common extended characters
    // This helps maintain proper spacing in commands like 'ps aux'
    .replace(/[\uFFFD]/g, '') // Remove Unicode replacement characters (�)

    // Clean up excessive whitespace while preserving formatting
    .replace(/[ \t]+$/gm, '') // Remove trailing spaces/tabs from each line
    .replace(/^\s*\n/gm, '\n') // Remove empty lines with only whitespace
    .trim();

  // Apply encoding fixes to handle "?" character issues
  return fixTerminalEncoding(cleaned);
};

/**
 * Formats terminal output for better readability
 * @param text - Terminal output text
 * @returns Formatted text with proper spacing and line breaks
 */
export const formatTerminalOutput = (text: string): string => {
  const decoded = decodeTerminalOutput(text);
  const encodingFixed = fixTerminalEncoding(decoded);

  // For terminal output, preserve original spacing and formatting
  // This is especially important for commands like ps aux where column alignment matters
  return encodingFixed;
};

/**
 * Checks if terminal output contains binary data
 * @param text - Terminal output text
 * @returns True if the text appears to contain binary data
 */
export const isBinaryOutput = (text: string): boolean => {
  if (!text) return false;

  // Check for high percentage of non-printable characters
  const nonPrintableCount = (text.match(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F-\xFF]/g) || []).length;
  const totalLength = text.length;

  return totalLength > 0 && (nonPrintableCount / totalLength) > 0.3;
};

/**
 * Truncates long terminal output for preview display
 * @param text - Terminal output text
 * @param maxLines - Maximum number of lines to show
 * @returns Truncated text with indication if content was cut
 */
export const truncateTerminalOutput = (text: string, maxLines: number = 10): string => {
  if (!text) return text;

  const lines = text.split('\n');
  if (lines.length <= maxLines) {
    return text;
  }

  const truncated = lines.slice(0, maxLines).join('\n');
  const remainingLines = lines.length - maxLines;

  return `${truncated}\n\n... (${remainingLines} more lines - click arrow to view full output)`;
};

/**
 * Enhanced character encoding fix for terminal output
 * Handles common encoding issues that cause "?" characters
 * @param text - Terminal output text that may have encoding issues
 * @returns Text with encoding issues resolved
 */
export const fixTerminalEncoding = (text: string): string => {
  if (!text) return text;

  // Handle common encoding issues that cause "?" characters
  let result = text
    // Fix common UTF-8 encoding issues
    .replace(/\uFFFD/g, '') // Remove Unicode replacement characters
    .replace(/\?{3,}/g, '??') // Replace 3+ consecutive ? with just 2

    // Handle specific terminal characters that might be misencoded
    .replace(/\x80/g, '€') // Euro symbol
    .replace(/\x81/g, '') // Unused
    .replace(/\x82/g, '\u201A') // Single low-9 quotation mark
    .replace(/\x83/g, '\u0192') // Latin small letter f with hook
    .replace(/\x84/g, '\u201E') // Double low-9 quotation mark
    .replace(/\x85/g, '\u2026') // Horizontal ellipsis
    .replace(/\x86/g, '\u2020') // Dagger
    .replace(/\x87/g, '\u2021') // Double dagger
    .replace(/\x88/g, '\u02C6') // Modifier letter circumflex accent
    .replace(/\x89/g, '\u2030') // Per mille sign
    .replace(/\x8A/g, '\u0160') // Latin capital letter S with caron
    .replace(/\x8B/g, '\u2039') // Single left-pointing angle quotation mark
    .replace(/\x8C/g, '\u0152') // Latin capital ligature OE
    .replace(/\x8D/g, '') // Unused
    .replace(/\x8E/g, '\u017D') // Latin capital letter Z with caron
    .replace(/\x8F/g, '') // Unused
    .replace(/\x90/g, '') // Unused
    .replace(/\x91/g, '\u2018') // Left single quotation mark
    .replace(/\x92/g, '\u2019') // Right single quotation mark
    .replace(/\x93/g, '\u201C') // Left double quotation mark
    .replace(/\x94/g, '\u201D') // Right double quotation mark
    .replace(/\x95/g, '\u2022') // Bullet
    .replace(/\x96/g, '\u2013') // En dash
    .replace(/\x97/g, '\u2014') // Em dash
    .replace(/\x98/g, '\u02DC') // Small tilde
    .replace(/\x99/g, '\u2122') // Trade mark sign
    .replace(/\x9A/g, '\u0161') // Latin small letter s with caron
    .replace(/\x9B/g, '\u203A') // Single right-pointing angle quotation mark
    .replace(/\x9C/g, '\u0153') // Latin small ligature oe
    .replace(/\x9D/g, '') // Unused
    .replace(/\x9E/g, '\u017E') // Latin small letter z with caron
    .replace(/\x9F/g, '\u0178'); // Latin capital letter Y with diaeresis

  // Additional cleanup for common terminal artifacts
  result = result
    // Handle box drawing characters that might be misencoded
    .replace(/\xB0/g, '°') // Degree symbol
    .replace(/\xB1/g, '±') // Plus-minus sign
    .replace(/\xB2/g, '²') // Superscript two
    .replace(/\xB3/g, '³') // Superscript three
    .replace(/\xB5/g, 'µ') // Micro sign
    .replace(/\xB6/g, '¶') // Pilcrow sign
    .replace(/\xB7/g, '·') // Middle dot
    .replace(/\xB9/g, '¹') // Superscript one
    .replace(/\xBA/g, 'º') // Masculine ordinal indicator
    .replace(/\xBC/g, '¼') // Vulgar fraction one quarter
    .replace(/\xBD/g, '½') // Vulgar fraction one half
    .replace(/\xBE/g, '¾') // Vulgar fraction three quarters

    // Handle accented characters
    .replace(/\xC0/g, 'À').replace(/\xC1/g, 'Á').replace(/\xC2/g, 'Â').replace(/\xC3/g, 'Ã')
    .replace(/\xC4/g, 'Ä').replace(/\xC5/g, 'Å').replace(/\xC6/g, 'Æ').replace(/\xC7/g, 'Ç')
    .replace(/\xC8/g, 'È').replace(/\xC9/g, 'É').replace(/\xCA/g, 'Ê').replace(/\xCB/g, 'Ë')
    .replace(/\xCC/g, 'Ì').replace(/\xCD/g, 'Í').replace(/\xCE/g, 'Î').replace(/\xCF/g, 'Ï')
    .replace(/\xD0/g, 'Ð').replace(/\xD1/g, 'Ñ').replace(/\xD2/g, 'Ò').replace(/\xD3/g, 'Ó')
    .replace(/\xD4/g, 'Ô').replace(/\xD5/g, 'Õ').replace(/\xD6/g, 'Ö').replace(/\xD7/g, '×')
    .replace(/\xD8/g, 'Ø').replace(/\xD9/g, 'Ù').replace(/\xDA/g, 'Ú').replace(/\xDB/g, 'Û')
    .replace(/\xDC/g, 'Ü').replace(/\xDD/g, 'Ý').replace(/\xDE/g, 'Þ').replace(/\xDF/g, 'ß')
    .replace(/\xE0/g, 'à').replace(/\xE1/g, 'á').replace(/\xE2/g, 'â').replace(/\xE3/g, 'ã')
    .replace(/\xE4/g, 'ä').replace(/\xE5/g, 'å').replace(/\xE6/g, 'æ').replace(/\xE7/g, 'ç')
    .replace(/\xE8/g, 'è').replace(/\xE9/g, 'é').replace(/\xEA/g, 'ê').replace(/\xEB/g, 'ë')
    .replace(/\xEC/g, 'ì').replace(/\xED/g, 'í').replace(/\xEE/g, 'î').replace(/\xEF/g, 'ï')
    .replace(/\xF0/g, 'ð').replace(/\xF1/g, 'ñ').replace(/\xF2/g, 'ò').replace(/\xF3/g, 'ó')
    .replace(/\xF4/g, 'ô').replace(/\xF5/g, 'õ').replace(/\xF6/g, 'ö').replace(/\xF7/g, '÷')
    .replace(/\xF8/g, 'ø').replace(/\xF9/g, 'ù').replace(/\xFA/g, 'ú').replace(/\xFB/g, 'û')
    .replace(/\xFC/g, 'ü').replace(/\xFD/g, 'ý').replace(/\xFE/g, 'þ').replace(/\xFF/g, 'ÿ');

  return result;
};
