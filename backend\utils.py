"""
Utility functions for the WebSocket-based VPS Admin backend.
"""

import asyncio
import hashlib
import json
import re
import secrets
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Set
import structlog

logger = structlog.get_logger(__name__)

def generate_id(prefix: str = "", length: int = 8) -> str:
    """Generate a random ID with optional prefix."""
    random_part = secrets.token_urlsafe(length)
    return f"{prefix}_{random_part}" if prefix else random_part

def hash_string(text: str) -> str:
    """Generate SHA256 hash of a string."""
    return hashlib.sha256(text.encode()).hexdigest()

def extract_placeholders(text: str) -> Set[str]:
    """Extract placeholders from text in format {placeholder_name}."""
    return set(re.findall(r'\{(\w+)\}', text))

def substitute_placeholders(text: str, values: Dict[str, str]) -> str:
    """Substitute placeholders in text with provided values."""
    for key, value in values.items():
        text = text.replace(f"{{{key}}}", value)
    return text

def sanitize_command(command: str) -> str:
    """Sanitize command for safe execution."""
    # Remove dangerous characters and patterns
    dangerous_patterns = [
        r';\s*rm\s+-rf\s+/',
        r';\s*dd\s+if=',
        r';\s*mkfs',
        r';\s*shutdown',
        r';\s*reboot',
        r';\s*halt',
        r'>\s*/dev/sd[a-z]',
        r'cat\s+/dev/urandom',
        r':\(\)\{\s*:\|:\&\s*\}',  # Fork bomb pattern
    ]
    
    for pattern in dangerous_patterns:
        if re.search(pattern, command, re.IGNORECASE):
            logger.warning("Dangerous command pattern detected", 
                         command=command[:100], 
                         pattern=pattern)
            raise ValueError(f"Command contains dangerous pattern: {pattern}")
    
    return command.strip()

def validate_hostname(hostname: str) -> bool:
    """Validate hostname format."""
    if not hostname or len(hostname) > 253:
        return False
    
    # Check for valid hostname pattern
    hostname_pattern = re.compile(
        r'^(?!-)[A-Za-z0-9-]{1,63}(?<!-)$|'
        r'^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$'
    )
    
    parts = hostname.split('.')
    return all(hostname_pattern.match(part) for part in parts)

def validate_port(port: int) -> bool:
    """Validate port number."""
    return 1 <= port <= 65535

def format_duration(seconds: float) -> str:
    """Format duration in seconds to human-readable string."""
    if seconds < 60:
        return f"{seconds:.1f}s"
    elif seconds < 3600:
        minutes = seconds / 60
        return f"{minutes:.1f}m"
    else:
        hours = seconds / 3600
        return f"{hours:.1f}h"

def format_bytes(bytes_count: int) -> str:
    """Format bytes to human-readable string."""
    for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
        if bytes_count < 1024.0:
            return f"{bytes_count:.1f}{unit}"
        bytes_count /= 1024.0
    return f"{bytes_count:.1f}PB"

def parse_command_output(output: str) -> Dict[str, Any]:
    """Parse command output and extract useful information."""
    lines = output.strip().split('\n')
    
    result = {
        "line_count": len(lines),
        "total_length": len(output),
        "lines": lines,
        "summary": output[:200] + "..." if len(output) > 200 else output
    }
    
    # Try to detect structured output (JSON, key-value pairs, etc.)
    try:
        # Try parsing as JSON
        json_data = json.loads(output)
        result["format"] = "json"
        result["parsed_data"] = json_data
    except json.JSONDecodeError:
        # Check for key-value pairs
        kv_pairs = {}
        for line in lines:
            if ':' in line and '=' not in line:
                parts = line.split(':', 1)
                if len(parts) == 2:
                    key = parts[0].strip()
                    value = parts[1].strip()
                    kv_pairs[key] = value
        
        if kv_pairs:
            result["format"] = "key_value"
            result["parsed_data"] = kv_pairs
        else:
            result["format"] = "text"
    
    return result

def is_safe_path(path: str) -> bool:
    """Check if a file path is safe (no directory traversal)."""
    # Normalize the path
    normalized = os.path.normpath(path)
    
    # Check for directory traversal attempts
    if '..' in normalized or normalized.startswith('/'):
        return False
    
    # Check for dangerous paths
    dangerous_paths = [
        '/etc/passwd', '/etc/shadow', '/etc/hosts',
        '/proc/', '/sys/', '/dev/',
        '/boot/', '/root/'
    ]
    
    for dangerous in dangerous_paths:
        if normalized.startswith(dangerous):
            return False
    
    return True

def rate_limit_key(user_id: str, action: str) -> str:
    """Generate rate limiting key."""
    return f"rate_limit:{user_id}:{action}"

def create_error_response(message: str, error_code: str = None, details: Dict[str, Any] = None) -> Dict[str, Any]:
    """Create standardized error response."""
    return {
        "success": False,
        "error": message,
        "error_code": error_code,
        "details": details or {},
        "timestamp": datetime.utcnow().isoformat()
    }

def create_success_response(data: Any = None, message: str = "Success") -> Dict[str, Any]:
    """Create standardized success response."""
    return {
        "success": True,
        "message": message,
        "data": data,
        "timestamp": datetime.utcnow().isoformat()
    }

class AsyncTimer:
    """Async context manager for timing operations."""
    
    def __init__(self, name: str = "operation"):
        self.name = name
        self.start_time = None
        self.end_time = None
    
    async def __aenter__(self):
        self.start_time = datetime.utcnow()
        logger.debug(f"Starting {self.name}")
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        self.end_time = datetime.utcnow()
        duration = (self.end_time - self.start_time).total_seconds()
        
        if exc_type:
            logger.error(f"{self.name} failed", 
                        duration=duration, 
                        error=str(exc_val))
        else:
            logger.info(f"{self.name} completed", duration=duration)

class CircularBuffer:
    """Thread-safe circular buffer for storing recent items."""
    
    def __init__(self, max_size: int):
        self.max_size = max_size
        self.buffer: List[Any] = []
        self.lock = asyncio.Lock()
    
    async def add(self, item: Any):
        """Add item to buffer."""
        async with self.lock:
            self.buffer.append(item)
            if len(self.buffer) > self.max_size:
                self.buffer.pop(0)
    
    async def get_all(self) -> List[Any]:
        """Get all items in buffer."""
        async with self.lock:
            return self.buffer.copy()
    
    async def get_recent(self, count: int) -> List[Any]:
        """Get most recent items."""
        async with self.lock:
            return self.buffer[-count:] if count < len(self.buffer) else self.buffer.copy()
    
    async def clear(self):
        """Clear buffer."""
        async with self.lock:
            self.buffer.clear()

def mask_sensitive_data(data: str, patterns: List[str] = None) -> str:
    """Mask sensitive data in strings."""
    if patterns is None:
        patterns = [
            r'password["\s]*[:=]["\s]*([^"\s,}]+)',
            r'token["\s]*[:=]["\s]*([^"\s,}]+)',
            r'key["\s]*[:=]["\s]*([^"\s,}]+)',
            r'secret["\s]*[:=]["\s]*([^"\s,}]+)',
        ]
    
    masked_data = data
    for pattern in patterns:
        masked_data = re.sub(pattern, r'\1***MASKED***', masked_data, flags=re.IGNORECASE)
    
    return masked_data

def validate_json_schema(data: Dict[str, Any], schema: Dict[str, Any]) -> List[str]:
    """Simple JSON schema validation. Returns list of errors."""
    errors = []
    
    def validate_field(field_name: str, value: Any, field_schema: Dict[str, Any]):
        field_type = field_schema.get('type')
        required = field_schema.get('required', False)
        
        if value is None:
            if required:
                errors.append(f"Field '{field_name}' is required")
            return
        
        if field_type == 'string' and not isinstance(value, str):
            errors.append(f"Field '{field_name}' must be a string")
        elif field_type == 'integer' and not isinstance(value, int):
            errors.append(f"Field '{field_name}' must be an integer")
        elif field_type == 'boolean' and not isinstance(value, bool):
            errors.append(f"Field '{field_name}' must be a boolean")
        elif field_type == 'array' and not isinstance(value, list):
            errors.append(f"Field '{field_name}' must be an array")
        elif field_type == 'object' and not isinstance(value, dict):
            errors.append(f"Field '{field_name}' must be an object")
        
        # Check string length
        if field_type == 'string' and isinstance(value, str):
            min_length = field_schema.get('min_length')
            max_length = field_schema.get('max_length')
            
            if min_length and len(value) < min_length:
                errors.append(f"Field '{field_name}' must be at least {min_length} characters")
            if max_length and len(value) > max_length:
                errors.append(f"Field '{field_name}' must be at most {max_length} characters")
        
        # Check enum values
        enum_values = field_schema.get('enum')
        if enum_values and value not in enum_values:
            errors.append(f"Field '{field_name}' must be one of: {enum_values}")
    
    # Validate each field in schema
    properties = schema.get('properties', {})
    for field_name, field_schema in properties.items():
        value = data.get(field_name)
        validate_field(field_name, value, field_schema)
    
    return errors

import os
