/**
 * Statistics tracking hook for VPS Admin Chat
 */

import { useState, useCallback, useEffect, useRef } from 'react';
import { ExecutionStats, UseStatsReturn } from '@/types';
import { DEFAULTS, STATS_UPDATE_INTERVAL } from '@/constants';

export const useStats = (): UseStatsReturn => {
  const [executionStats, setExecutionStats] = useState<ExecutionStats>({
    totalCommands: DEFAULTS.EXECUTION_TIME,
    successfulCommands: DEFAULTS.EXECUTION_TIME,
    failedCommands: DEFAULTS.EXECUTION_TIME,
    averageExecutionTime: DEFAULTS.EXECUTION_TIME,
    uptime: DEFAULTS.UPTIME
  });

  const startTimeRef = useRef<Date | null>(null);
  const executionTimesRef = useRef<number[]>([]);

  // Initialize start time only on client side
  useEffect(() => {
    if (startTimeRef.current === null) {
      startTimeRef.current = new Date();
    }
  }, []);

  // Update uptime every second - only after client-side initialization
  useEffect(() => {
    if (startTimeRef.current === null) return;

    const interval = setInterval(() => {
      if (startTimeRef.current) {
        setExecutionStats(prev => ({
          ...prev,
          uptime: Math.floor((Date.now() - startTimeRef.current!.getTime()) / 1000)
        }));
      }
    }, STATS_UPDATE_INTERVAL);

    return () => clearInterval(interval);
  }, [startTimeRef.current]);

  const updateStats = useCallback((type: 'command_success' | 'command_failure', duration?: number) => {
    setExecutionStats(prev => {
      const newStats = { ...prev };

      // Update command counts
      newStats.totalCommands += 1;
      if (type === 'command_success') {
        newStats.successfulCommands += 1;
      } else {
        newStats.failedCommands += 1;
      }

      // Update average execution time if duration provided
      if (duration !== undefined) {
        executionTimesRef.current.push(duration);
        // Keep only last 100 execution times for average calculation
        if (executionTimesRef.current.length > 100) {
          executionTimesRef.current = executionTimesRef.current.slice(-100);
        }

        const sum = executionTimesRef.current.reduce((acc, time) => acc + time, 0);
        newStats.averageExecutionTime = Math.round(sum / executionTimesRef.current.length);
      }

      return newStats;
    });
  }, []);

  const resetStats = useCallback(() => {
    setExecutionStats({
      totalCommands: 0,
      successfulCommands: 0,
      failedCommands: 0,
      averageExecutionTime: 0,
      uptime: 0
    });
    // Only reset start time on client side
    if (typeof window !== 'undefined') {
      startTimeRef.current = new Date();
    }
    executionTimesRef.current = [];
  }, []);





  return {
    // State
    executionStats,

    // Core operations
    updateStats,
    resetStats
  };
};
