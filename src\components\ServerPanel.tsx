/**
 * ServerPanel component for server switching and management
 */

import React, { useState } from 'react';
import { Server, Plus, Wifi, WifiOff, Loader, Trash2 } from 'lucide-react';
import { motion } from 'framer-motion';
import { ServerPanelProps } from '@/types';
import ConfirmationModal from './ConfirmationModal';

const ServerPanel: React.FC<ServerPanelProps> = ({
  servers,
  currentServerId,
  onServerSelect,
  onAddServer,
  onDeleteServer
}) => {
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [serverToDelete, setServerToDelete] = useState<string | null>(null);
  const getStatusColor = (status: 'connected' | 'disconnected') => {
    switch (status) {
      case 'connected':
        return 'border-theme-primary bg-surface-primary';
      case 'disconnected':
        return 'border-theme-primary bg-surface-primary'; // Keep consistent background
      default:
        return 'border-theme-primary bg-surface-primary';
    }
  };

  const handleDeleteClick = (e: React.MouseEvent, serverId: string) => {
    e.stopPropagation();
    setServerToDelete(serverId);
    setDeleteModalOpen(true);
  };

  const handleDeleteConfirm = () => {
    if (serverToDelete) {
      onDeleteServer(serverToDelete);
      setDeleteModalOpen(false);
      setServerToDelete(null);
    }
  };

  const handleDeleteCancel = () => {
    setDeleteModalOpen(false);
    setServerToDelete(null);
  };

  return (
    <div className="flex flex-col h-full">
      {/* Panel Header */}
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-sm font-semibold text-theme-primary flex items-center gap-2">
          <Server size={16} className="text-accent-primary" />
          Servers
        </h3>
      </div>

      {/* Server List - Scrollable */}
      <div className="flex-1 overflow-y-auto overflow-x-hidden minimal-scrollbar">
        <div className="space-y-2">
          {servers.map((server) => (
            <motion.button
              key={server.id}
              whileHover={{ scale: 1.01 }}
              whileTap={{ scale: 0.99 }}
              onClick={() => onServerSelect(server.id)}
              className={`
                w-full p-3 rounded-lg border transition-all duration-200 text-left relative overflow-hidden
                ${currentServerId === server.id
                  ? 'border-accent-primary bg-accent-primary/10'
                  : `${getStatusColor(server.status)} hover:bg-surface-secondary hover:border-accent-primary/50`
                }
              `}
            >
              {/* Active Server Indicator */}
              {currentServerId === server.id && (
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-accent-primary rounded-full border-2 border-theme-primary"></div>
              )}

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3 min-w-0 flex-1">
                  {/* Server Icon */}
                  <div className="flex-shrink-0 w-8 h-8 rounded-full bg-theme-secondary flex items-center justify-center">
                    <Server size={16} className="text-theme-primary" />
                  </div>

                  {/* Server Info */}
                  <div className="min-w-0 flex-1">
                    <div className="font-medium text-theme-primary text-sm truncate">
                      {server.name}
                    </div>
                    <div className="text-xs text-theme-tertiary truncate">
                      {server.host}
                    </div>
                  </div>
                </div>

                {/* Actions */}
                <div className="flex items-center gap-2 flex-shrink-0">
                  {/* Status Indicator */}
                  <div className={`w-2 h-2 rounded-full ${server.status === 'connected' ? 'bg-green-500' : 'bg-red-500'}`}></div>

                  {/* Delete Button */}
                  <motion.button
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                    onClick={(e) => handleDeleteClick(e, server.id)}
                    className="p-1 rounded-lg text-red-500 hover:text-red-600 hover:bg-red-50 dark:hover:bg-red-950/20 transition-colors"
                    title="Delete Server"
                  >
                    <Trash2 size={14} />
                  </motion.button>
                </div>
              </div>
            </motion.button>
          ))}
        </div>
      </div>

      {/* Add Server Button - Sticky Bottom */}
      <div className="mt-3 pt-3 border-t border-theme-primary">
        <motion.button
          whileHover={{ scale: 1.01 }}
          whileTap={{ scale: 0.99 }}
          onClick={onAddServer}
          className="w-full p-3 border border-dashed border-theme-primary hover:border-accent-primary rounded-lg transition-all duration-200 group hover:bg-surface-secondary"
        >
          <div className="flex items-center justify-center gap-2 text-theme-tertiary group-hover:text-accent-primary">
            <Plus size={16} />
            <span className="text-sm font-medium">Add Server</span>
          </div>
        </motion.button>
      </div>

      {/* Delete Confirmation Modal */}
      <ConfirmationModal
        isOpen={deleteModalOpen}
        onClose={handleDeleteCancel}
        onConfirm={handleDeleteConfirm}
        title="Delete Server"
        message={`Are you sure you want to delete "${servers.find(s => s.id === serverToDelete)?.name}"? This action cannot be undone.`}
        confirmText="Delete"
        cancelText="Cancel"
        confirmButtonStyle="danger"
      />
    </div>
  );
};

export default ServerPanel;
