/**
 * Stream handling hook for VPS Admin Chat
 * Uses WebSocket for real-time communication with the backend
 */

import { useCallback, useRef, useEffect } from 'react';
import { UseStreamHandlerReturn, SSEEventData } from '@/types';
import { websocketService, WebSocketMessage } from '@/services/websocketService';
import { safeJsonParse, debugJsonString } from '@/utils';

export const useStreamHandler = (
  onMessage: (data: SSEEventData) => void,
  onError: (error: Error) => void,
  onClose: () => void
): UseStreamHandlerReturn => {
  const isStreamActiveRef = useRef<boolean>(false);
  const currentTaskIdRef = useRef<string | null>(null);

  // Convert WebSocket message to SSE format for compatibility
  const convertWebSocketToSSE = useCallback((wsMessage: WebSocketMessage): SSEEventData => {
    return {
      type: wsMessage.type,
      content: wsMessage.content,
      data: wsMessage.content,
      metadata: wsMessage.metadata,
      timestamp: wsMessage.timestamp,
      task_id: wsMessage.task_id
    };
  }, []);

  // Handle WebSocket messages for the current task
  const handleWebSocketMessage = useCallback((wsMessage: WebSocketMessage) => {
    // Only process messages for the current task
    if (currentTaskIdRef.current && wsMessage.task_id === currentTaskIdRef.current) {
      try {
        const sseData = convertWebSocketToSSE(wsMessage);
        console.log(`DEBUG: Received WebSocket message for task ${wsMessage.task_id}:`, sseData);
        onMessage(sseData);

        // Check if this is a completion message
        if (wsMessage.type === 'task_complete' || wsMessage.type === 'error') {
          console.log(`DEBUG: Task ${wsMessage.task_id} completed or errored`);
          isStreamActiveRef.current = false;
          currentTaskIdRef.current = null;
          onClose();
        }
      } catch (error) {
        console.error('Error processing WebSocket message:', error);
        onError(new Error(`Failed to process message: ${error instanceof Error ? error.message : 'Unknown error'}`));
      }
    }
  }, [onMessage, onError, onClose, convertWebSocketToSSE]);

  // Set up WebSocket message listener
  useEffect(() => {
    const unsubscribe = websocketService.onMessage(handleWebSocketMessage);
    return unsubscribe;
  }, [handleWebSocketMessage]);

  const handleStream = useCallback(async (taskId: string, message: string) => {
    try {
      console.log(`DEBUG: Starting WebSocket stream for task ${taskId} with message: ${message}`);

      // Abort any existing stream
      abortStream();

      // Set stream as active
      isStreamActiveRef.current = true;
      currentTaskIdRef.current = taskId;

      // Ensure WebSocket is connected
      if (!websocketService.isConnected()) {
        console.log("WebSocket not connected, attempting to connect...");
        const token = localStorage.getItem('vps_admin_token') || 'demo-token';
        await websocketService.connect(token);
      }

      // Subscribe to task updates
      websocketService.subscribeToTask(taskId);

      // Send the message via WebSocket
      websocketService.sendTaskMessage(taskId, message);

    } catch (error) {
      console.error("Error starting WebSocket stream:", error);
      isStreamActiveRef.current = false;
      currentTaskIdRef.current = null;
      onError(new Error(`Failed to start stream: ${error instanceof Error ? error.message : 'Unknown error'}`));
    }
  }, [onError]);

  const abortStream = useCallback(() => {
    console.log("DEBUG: Aborting WebSocket stream");
    
    // Unsubscribe from current task if any
    if (currentTaskIdRef.current) {
      try {
        websocketService.unsubscribeFromTask(currentTaskIdRef.current);
      } catch (error) {
        console.warn("Failed to unsubscribe from task:", error);
      }
    }

    isStreamActiveRef.current = false;
    currentTaskIdRef.current = null;

    // Note: We don't disconnect the WebSocket here as it might be used for other tasks
    // The WebSocket connection is managed globally by the websocketService
  }, []);

  const isStreamActive = useCallback(() => {
    return isStreamActiveRef.current && websocketService.isConnected();
  }, []);

  const getStreamState = useCallback(() => {
    if (isStreamActiveRef.current) {
      return websocketService.getConnectionState();
    }
    return 'INACTIVE';
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      abortStream();
    };
  }, [abortStream]);

  return {
    handleStream,
    abortStream,
    isStreamActive,
    getStreamState
  };
};
