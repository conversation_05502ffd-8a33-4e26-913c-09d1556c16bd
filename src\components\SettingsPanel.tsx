/**
 * SettingsPanel component for displaying settings, profile, and quick actions
 */

import React from 'react';
import { Settings, XCircle, Download, Play, Pause, LogOut, Moon, Sun, User } from 'lucide-react';
import { SettingsPanelProps } from '@/types';
import { useTheme } from '@/hooks/useTheme';
import { useAuth } from '@/components/AuthProvider';
import { motion } from 'framer-motion';

const SettingsPanel: React.FC<SettingsPanelProps> = ({
  executionStats,
  currentTask,
  taskHistory,
  messages,
  autoScroll,
  onClose,
  onExportHistory,
  onToggleAutoScroll,
  onLogoutRequest,
  wsConnected = true
}) => {
  const { toggleTheme, getThemeIcon, getThemeLabel } = useTheme();
  const { user } = useAuth();

  // Debug log to verify user object structure
  console.log('User object in SettingsPanel:', user);

  const getIcon = () => {
    const iconName = getThemeIcon();
    switch (iconName) {
      case 'Sun':
        return <Sun size={14} />;
      case 'Moon':
        return <Moon size={14} />;
      default:
        return <Sun size={14} />;
    }
  };

  const formatUptime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    return `${hours}h ${minutes}m ${secs}s`;
  };

  return (
    <div className="bg-theme-primary border-0 lg:border border-theme-primary rounded-none lg:rounded-xl shadow-none lg:shadow-2xl h-full w-full lg:w-[calc(100%-15px)] p-3 lg:p-4 overflow-y-auto transition-all duration-300 flex flex-col">
      <div className="flex items-center justify-between mb-3 lg:mb-4">
        <h3 className="font-semibold text-theme-primary flex items-center gap-2">
          <Settings size={16} className="text-accent-primary" />
          Settings
        </h3>
        <button
          onClick={onClose}
          className="text-theme-tertiary hover:text-theme-secondary dark:hover:text-theme-primary transition-colors p-1 touch-manipulation rounded-lg hover:bg-surface-secondary"
          title="Close Settings Panel"
        >
          <XCircle size={16} />
        </button>
      </div>

      <div className="space-y-4 flex-1">
        {/* Profile Section */}
        <div className="bg-surface-primary p-3 rounded-xl border border-theme-primary">
          <h4 className="font-medium text-sm text-theme-secondary mb-2">Profile</h4>
          {user && (
            <div className="flex items-center gap-3 p-2 bg-surface-secondary rounded-lg border border-theme-primary">
              <div className="flex items-center justify-center w-8 h-8 bg-accent-primary rounded-full">
                <User size={14} className="text-white" />
              </div>
              <div className="flex-1">
                <div className="text-sm font-medium text-theme-primary">{user.user_id}</div>
              </div>
            </div>
          )}
        </div>



        {/* Task History */}
        <div className="bg-surface-primary p-2 lg:p-3 rounded-xl border border-theme-primary">
          <h4 className="font-medium text-sm text-theme-secondary mb-2">Task History</h4>
          <div className="text-xs">
            <div className="flex justify-between text-theme-tertiary">
              <span>Total Tasks:</span>
              <span className="font-mono text-theme-primary">{taskHistory.length}</span>
            </div>
            {currentTask && (
              <div className="flex justify-between mt-1 text-theme-tertiary">
                <span>Current Task:</span>
                <span className="font-mono text-accent-secondary truncate max-w-24" title={currentTask.id}>
                  {currentTask.id.slice(0, 8)}...
                </span>
              </div>
            )}
          </div>
        </div>

        {/* Session Info */}
        <div className="bg-surface-primary p-3 rounded-xl border border-theme-primary">
          <h4 className="font-medium text-sm text-theme-secondary mb-2">Session Info</h4>
          <div className="text-xs">
            <div className="flex justify-between text-theme-tertiary">
              <span>Uptime:</span>
              <span className="font-mono text-theme-primary">{formatUptime(executionStats.uptime)}</span>
            </div>
            <div className="flex justify-between mt-1 text-theme-tertiary">
              <span>Messages:</span>
              <span className="font-mono text-theme-primary">{messages.length}</span>
            </div>
            <div className="flex justify-between mt-1 text-theme-tertiary">
              <span>Auto-scroll:</span>
              <span className={`font-mono ${autoScroll ? 'text-accent-secondary' : 'text-theme-quaternary'}`}>
                {autoScroll ? 'On' : 'Off'}
              </span>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="bg-surface-primary p-3 rounded-xl border border-theme-primary">
          <h4 className="font-medium text-sm text-theme-secondary mb-2">Quick Actions</h4>
          <div className="space-y-2">
            <button
              onClick={onExportHistory}
              className="w-full text-xs bg-accent-primary text-white px-3 py-2 rounded-lg hover:opacity-90 flex items-center gap-2 transition-all duration-200 hover:transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
              disabled={taskHistory.length === 0 && messages.length === 0}
            >
              <Download size={12} />
              Export History
            </button>
            <button
              onClick={onToggleAutoScroll}
              className={`w-full text-xs px-3 py-2 rounded-lg flex items-center gap-2 transition-all duration-200 hover:transform hover:scale-105 ${
                autoScroll
                  ? 'bg-accent-secondary text-white hover:opacity-90'
                  : 'bg-surface-secondary text-theme-primary border border-theme-primary hover:bg-theme-secondary'
              }`}
            >
              {autoScroll ? <Pause size={12} /> : <Play size={12} />}
              {autoScroll ? 'Disable' : 'Enable'} Auto-scroll
            </button>
          </div>
        </div>
      </div>

      {/* System Status and Controls - Fixed at bottom */}
      <div className="flex items-stretch gap-2 mt-4">
        {/* System Status */}
        <div className="bg-surface-primary p-3 rounded-xl border border-theme-primary transition-all duration-300 flex-1 flex items-center">
          <div className="flex items-center justify-between w-full">
            <h4 className="font-medium text-sm text-theme-secondary">Status</h4>
            <div className="flex items-center gap-1">
              <div className={`w-2 h-2 rounded-full ${wsConnected ? 'bg-accent-secondary animate-pulse' : 'bg-accent-error'}`}></div>
              <span className={`font-medium text-sm ${wsConnected ? 'text-accent-secondary' : 'text-accent-error'}`}>
                {wsConnected ? 'Active' : 'Disconnected'}
              </span>
            </div>
          </div>
        </div>

        {/* Controls */}
        <div className="bg-surface-primary p-3 rounded-xl border border-theme-primary transition-all duration-300 flex items-center">
          <div className="flex items-center gap-2">
            {/* Theme Toggle Button */}
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={toggleTheme}
              className="p-2 rounded-lg bg-theme-secondary text-theme-secondary hover:bg-theme-tertiary transition-colors"
              title={`Theme: ${getThemeLabel()}`}
            >
              {getIcon()}
            </motion.button>

            {/* Logout Button */}
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={onLogoutRequest}
              className="p-2 rounded-lg bg-theme-secondary text-theme-secondary hover:bg-theme-tertiary transition-colors"
              title="Logout"
            >
              <LogOut size={14} />
            </motion.button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SettingsPanel;
