#!/usr/bin/env python3
"""
Script to remove tenant_id references from WebSocket messages in task_manager.py
"""

import re

def remove_tenant_refs(file_path):
    """Remove tenant_id=user_context.tenant_id lines from WebSocket messages"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()

    # Pattern to match lines with tenant_id=user_context.tenant_id (including comma and whitespace)
    pattern = r',\s*\n\s*tenant_id=user_context\.tenant_id'

    # Replace the pattern with empty string
    updated_content = re.sub(pattern, '', content)

    # Also handle cases where tenant_id might be on the same line
    pattern2 = r',\s*tenant_id=user_context\.tenant_id'
    updated_content = re.sub(pattern2, '', updated_content)

    # Write back to file
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(updated_content)

    print(f"Updated {file_path}")

if __name__ == "__main__":
    remove_tenant_refs("backend/task_manager.py")
