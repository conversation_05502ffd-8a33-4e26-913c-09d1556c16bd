# ai_client.py
import os
import json
import re
import asyncio
from typing import Dict, Optional, List

# Using the correct import structure for google-generativeai
import google.generativeai as genai
from jsonschema import validate, ValidationError

# Task generation schema
TASK_SCHEMA = {
    "type": "object",
    "properties": {
        "task_name": {"type": "string"},
        "description": {"type": "string"},
        "steps": {
            "type": "array",
            "items": {
                "type": "object",
                "properties": {
                    "description": {"type": "string"},
                    "command": {"type": "string"},
                    "rollback_command": {"type": "string"},
                    "danger_level": {"type": "string", "enum": ["safe", "caution", "dangerous"]},
                    "interactive": {"type": "boolean"}
                },
                "required": ["description", "command", "interactive"]
            }
        },
        "conclusion": {"type": "string"}
    },
    "required": ["task_name", "description", "steps", "conclusion"]
}
ERROR_SCHEMA = {
    "type": "object",
    "properties": { "message": {"type": "string"}, },
    "required": ["message"]
}

class AIClient:
    """Handles all interactions with the Google Gemini API."""

    def __init__(self, api_key: str):
        if not api_key:
            raise ValueError("GEMINI_API_KEY is required to initialize the AIClient.")

        # Configure the API key
        genai.configure(api_key=api_key)
        self.model_name = os.getenv("GEMINI_MODEL", "gemini-1.5-pro-latest")

    def _execute_gemini_call(self, prompt: str, config_params: Dict) -> str:
        """
        A centralized, synchronous method for calling the Gemini API.
        """
        print("Executing Gemini Call")

        # Create the model
        model = genai.GenerativeModel(self.model_name)

        # Generate response
        response = model.generate_content(
            prompt,
            generation_config=config_params
        )

        return response.text if response.text else ""

    def _sync_generate_task_plan(self, user_request: str, target_os: str, system_context: str) -> Optional[Dict]:
        """Internal sync method to generate the main task plan."""

        # Create the prompt using the new template
        prompt = self._create_task_prompt(user_request, target_os, system_context)

        # Define the configuration for this specific call
        task_plan_config = {
            "temperature": 1,
        }

        try:
            response_text = self._execute_gemini_call(prompt, task_plan_config)
            return self._validate_and_parse_response(response_text)
        except Exception as e:
            print(f"Error calling Gemini API for task plan: {e}")
            return {"is_error": True, "data": {"message": f"Connection Error: {e}"}}

    async def generate_task_plan(self, user_request: str, target_os: str, system_context: str) -> Optional[Dict]:
        """Asynchronously generates a task plan by running the sync method in a thread."""
        return await asyncio.to_thread(
            self._sync_generate_task_plan, user_request, target_os, system_context
        )

    def _sync_generate_correction_command(self, step_description: str, failed_history: List[Dict]) -> str:
        """Internal sync method to generate a correction command."""
        prompt = self._create_correction_prompt(step_description, failed_history)

        # Define the configuration for this specific call
        correction_config = {
            "temperature": 0.4,
            "stop_sequences": ["\n"]
        }

        try:
            command = self._execute_gemini_call(prompt, correction_config)

            # Clean up the response
            command = command.strip()
            if "```" in command:
                command = re.sub(r"```(bash|sh)?\s*([\s\S]*?)\s*```", r"\2", command).strip()

            return command if command else "NO_ALTERNATIVE"
        except Exception as e:
            print(f"Error calling Gemini API for correction: {e}")
            return "NO_ALTERNATIVE"

    async def generate_correction_command(self, step_description: str, failed_history: List[Dict]) -> str:
        """Asynchronously generates a correction command."""
        return await asyncio.to_thread(
            self._sync_generate_correction_command, step_description, failed_history
        )
    
    def _create_correction_prompt(self, step_description: str, failed_history: List[Dict]) -> str:
        # Build the failure history string
        history_str = ""
        for i, attempt in enumerate(failed_history):
            history_str += f"\n--- ATTEMPT {i+1} ---\n"
            history_str += f"COMMAND: `{attempt['command']}`\n"
            history_str += f"STDOUT:\n{attempt['stdout']}\n"
            history_str += f"STDERR:\n{attempt['stderr']}\n"
            history_str += "---------------------\n"

        return f"""# Command Correction Expert

You are an expert Ubuntu server administrator specializing in command troubleshooting. Analyze failed commands and provide corrections.

**TASK STEP:** {step_description}

**FAILED COMMAND HISTORY:**
{history_str}

## CRITICAL: Response Format Rules

You MUST respond with ONLY a single line containing one of these responses:

**If the output shows an actual command failure that needs correction:**
- Return the corrected command as a single line
- Example: `apt-get install -y nginx`

**If the command worked correctly but produced expected output:**
- Return: `NOT_A_FAILURE`
- Use this for cases like: directory doesn't exist (when checking), file not found (when verifying), service not running (when checking status), etc.

**If no alternative command can fix the issue:**
- Return: `NO_ALTERNATIVE`

## Analysis Guidelines

- **NOT_A_FAILURE cases:** Commands that produce "error" messages but are actually working correctly
  - `ls /nonexistent` → "directory doesn't exist" (expected when checking)
  - `systemctl is-active stopped-service` → "inactive" (expected status check)
  - `grep pattern file` → "no matches" (expected when pattern not found)
  - `ping unreachable-host` → "unreachable" (expected network test result)

- **Actual failures:** Commands that failed due to syntax, permissions, missing packages, etc.
  - `apt-get install missing-package` → package not found
  - `systemctl start service` → permission denied
  - `cp source dest` → source file not found

## IMPORTANT
**Return ONLY one line. No explanations, comments, or additional text.**"""

    def _create_task_prompt(self, user_request: str, target_os: str, system_context: str) -> str:
        # Updated prompt with your exact specification
        # **SERVICES:** {system_context}
        return f"""# Server Admin Task Generator

You are an expert Ubuntu server administrator. You MUST generate task breakdowns for server administration requests.

**TARGET OS:** {target_os}
**EXAMPLE REQUEST:** {user_request}

## CRITICAL: Response Format Rules

You MUST respond with ONLY a JSON object. No additional text, explanations, or comments.

**If request is valid server administration:**
```json
{{
  "task_name": "Brief descriptive name",
  "description": "What this accomplishes",
  "steps": [
    {{
      "description": "What this step does",
      "command": "Exact terminal command",
      "rollback_command": "Revert command || no rollback available",
      "danger_level": "safe/caution/dangerous",
      "interactive": false
    }}
  ],
  "conclusion": "Summary of what was accomplished and next steps (do not mention placeholders)"
}}
```

**If request is invalid/unclear/dangerous:**
```json
{{
  "message": "Error explanation"
}}
```

## Requirements

- Ubuntu-specific commands with exact paths/options
- Mark dangerous operations clearly
- Use non-interactive commands (set `"interactive": true` only when absolutely necessary)
- **Avoid interactive commands like:** `nano`, `vim`, `crontab -e`, `visudo`, `systemctl edit`, `git commit` (without -m), `mysql`, `psql`
- **Use alternatives:** `echo "content" > file`, `crontab -l`, `EDITOR=cat crontab -e`
- For commands needing user data, use placeholders like `@@PLACEHOLDER_NAME@@`.
- Security best practices
- Simple tasks = 1 step, complex tasks = multiple steps
- Include only essential steps
- In conclusion, summarize what was accomplished without mentioning placeholders

## IMPORTANT
**Return ONLY the JSON object. No other text allowed.**"""

    def _validate_and_parse_response(self, json_response: str) -> Optional[Dict]:
        # This validation logic is correct and remains unchanged
        try:
            match = re.search(r"```json\s*([\s\S]*?)\s*```", json_response)
            if match:
                json_response = match.group(1)
            json_response = json_response.strip()

            data = json.loads(json_response)
        except json.JSONDecodeError:
            print(f"✗ Failed to parse JSON from AI response.\nRaw Response:\n{json_response}")
            return {"is_error": True, "data": {"message": "The AI returned an invalid response. Please try again."}}

        try:
            validate(instance=data, schema=TASK_SCHEMA)
            return {"is_error": False, "data": data}
        except ValidationError:
            try:
                validate(instance=data, schema=ERROR_SCHEMA)
                return {"is_error": True, "data": data}
            except ValidationError as final_e:
                print(f"✗ AI response failed validation for both task and error formats.")
                print(f"  Validation Error: {final_e.message}")
                print(f"  Raw Response:\n{json_response}")
                return {"is_error": True, "data": {"message": "The AI's response was not in a recognizable format."}}

    async def get_cache_stats(self) -> Dict:
        """Get cache statistics (simplified version for compatibility)."""
        return {
            "cache_size": 0,
            "max_cache_size": 0,
            "ttl_seconds": 0,
            "note": "Caching disabled in simplified AI client"
        }

# Global AI client instance (initialized when needed)
_ai_client: Optional[AIClient] = None

def get_ai_client() -> AIClient:
    """Get or create global AI client instance."""
    global _ai_client
    if _ai_client is None:
        from config import settings
        _ai_client = AIClient(settings.gemini_api_key)
    return _ai_client