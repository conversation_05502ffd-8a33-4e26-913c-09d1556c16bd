"""
Authentication and authorization module for the WebSocket-based VPS Admin backend.
Handles JWT tokens, user management, and security.
"""

import hashlib
import secrets
import warnings
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from fastapi import HTTPException, status, Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from jose import JWTError, jwt
from passlib.context import CryptContext
import structlog

# Suppress bcrypt warnings
warnings.filterwarnings("ignore", message=".*bcrypt.*", category=UserWarning)

from models import User, Token, UserCreate, UserLogin, UserContext
from config import settings

logger = structlog.get_logger(__name__)

# Password hashing with error suppression
try:
    pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
except Exception:
    # Fallback to basic bcrypt if there are version issues
    pwd_context = CryptContext(schemes=["bcrypt"])

# JWT token security
security = HTTPBearer()

# In-memory user storage (replace with database in production)
users_db: Dict[str, Dict[str, Any]] = {}
sessions_db: Dict[str, Dict[str, Any]] = {}

class AuthManager:
    """Handles authentication and authorization operations."""
    
    def __init__(self):
        self.secret_key = settings.secret_key
        self.algorithm = settings.algorithm
        self.access_token_expire_minutes = settings.access_token_expire_minutes
        
        # Create default admin user for development
        if settings.debug:
            self._create_default_users()
    
    def _create_default_users(self):
        """Create default users for development."""
        admin_user = {
            "id": "admin",
            "username": "admin",
            "email": "admin@localhost",
            "hashed_password": self.get_password_hash("admin123"),
            "is_active": True,
            "created_at": datetime.utcnow(),
            "permissions": ["admin", "user", "task:create", "task:read", "task:update", "task:delete"]
        }
        users_db["admin"] = admin_user

        demo_user = {
            "id": "demo",
            "username": "demo",
            "email": "demo@localhost",
            "hashed_password": self.get_password_hash("demo123"),
            "is_active": True,
            "created_at": datetime.utcnow(),
            "permissions": ["user", "task:create", "task:read"]
        }
        users_db["demo"] = demo_user
        
        logger.info("Created default users for development", users=["admin", "demo"])
    
    def verify_password(self, plain_password: str, hashed_password: str) -> bool:
        """Verify a password against its hash."""
        return pwd_context.verify(plain_password, hashed_password)
    
    def get_password_hash(self, password: str) -> str:
        """Hash a password."""
        return pwd_context.hash(password)
    
    def get_user(self, username: str) -> Optional[Dict[str, Any]]:
        """Get user by username."""
        return users_db.get(username)
    
    def authenticate_user(self, username: str, password: str) -> Optional[Dict[str, Any]]:
        """Authenticate a user with username and password."""
        user = self.get_user(username)
        if not user:
            return None

        if not self.verify_password(password, user["hashed_password"]):
            return None

        # Update last login
        user["last_login"] = datetime.utcnow()

        return user
    
    def create_access_token(self, data: Dict[str, Any], expires_delta: Optional[timedelta] = None) -> str:
        """Create a JWT access token."""
        to_encode = data.copy()
        
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(minutes=self.access_token_expire_minutes)
        
        to_encode.update({"exp": expire})
        encoded_jwt = jwt.encode(to_encode, self.secret_key, algorithm=self.algorithm)
        
        return encoded_jwt
    
    def verify_token(self, token: str) -> Optional[Dict[str, Any]]:
        """Verify and decode a JWT token."""
        # Special case for development: accept 'demo-token'
        if token == 'demo-token':
            return {
                "username": "demo",
                "user_id": "demo",
                "tenant_id": "default",
                "permissions": ["user", "task:create", "task:read", "task:update"]
            }

        try:
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            username: str = payload.get("sub")
            user_id: str = payload.get("user_id")
            tenant_id: str = payload.get("tenant_id")

            if username is None or user_id is None:
                return None

            return {
                "username": username,
                "user_id": user_id,
                "tenant_id": tenant_id,
                "permissions": payload.get("permissions", [])
            }
        except JWTError:
            return None
    
    def create_user(self, user_data: UserCreate) -> User:
        """Create a new user."""
        # Check if user already exists
        if self.get_user(user_data.username):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Username already registered"
            )

        # Generate user ID
        user_id = f"user_{secrets.token_urlsafe(8)}"

        # Create user record
        user_record = {
            "id": user_id,
            "username": user_data.username,
            "email": user_data.email,
            "hashed_password": self.get_password_hash(user_data.password),
            "is_active": True,
            "created_at": datetime.utcnow(),
            "permissions": ["user", "task:create", "task:read"]
        }
        
        users_db[user_data.username] = user_record

        return User(
            id=user_id,
            username=user_data.username,
            email=user_data.email,
            is_active=True,
            created_at=user_record["created_at"]
        )
    
    def login(self, login_data: UserLogin) -> Token:
        """Login a user and return a token."""
        user = self.authenticate_user(
            login_data.username,
            login_data.password
        )
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Incorrect username or password",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        if not user["is_active"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Inactive user"
            )
        
        # Create access token
        access_token_expires = timedelta(minutes=self.access_token_expire_minutes)
        access_token = self.create_access_token(
            data={
                "sub": user["username"],
                "user_id": user["id"],
                "permissions": user.get("permissions", [])
            },
            expires_delta=access_token_expires
        )

        # Store session
        session_id = secrets.token_urlsafe(32)
        sessions_db[session_id] = {
            "user_id": user["id"],
            "username": user["username"],
            "created_at": datetime.utcnow(),
            "expires_at": datetime.utcnow() + access_token_expires
        }

        return Token(
            access_token=access_token,
            token_type="bearer",
            expires_in=self.access_token_expire_minutes * 60,
            user_id=user["id"]
        )
    
    def get_current_user_context(self, token_data: Dict[str, Any]) -> UserContext:
        """Get current user context from token data."""
        return UserContext(
            user_id=token_data["user_id"],
            permissions=token_data.get("permissions", []),
            settings={}
        )

    def check_permission(self, user_context: UserContext, required_permission: str) -> bool:
        """Check if user has required permission."""
        if "admin" in user_context.permissions:
            return True

        return required_permission in user_context.permissions

# Global auth manager instance
auth_manager = AuthManager()

# Dependency functions for FastAPI
async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)) -> UserContext:
    """Get current authenticated user context."""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )

    try:
        token_data = auth_manager.verify_token(credentials.credentials)
        if token_data is None:
            raise credentials_exception

        return auth_manager.get_current_user_context(token_data)
    except Exception:
        raise credentials_exception

async def get_current_active_user(current_user: UserContext = Depends(get_current_user)) -> UserContext:
    """Get current active user context."""
    # Additional checks can be added here
    return current_user

def require_permission(permission: str):
    """Decorator to require specific permission."""
    def permission_checker(current_user: UserContext = Depends(get_current_active_user)) -> UserContext:
        if not auth_manager.check_permission(current_user, permission):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Permission '{permission}' required"
            )
        return current_user

    return permission_checker
