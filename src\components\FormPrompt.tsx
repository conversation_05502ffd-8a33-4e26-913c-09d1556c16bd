/**
 * FormPrompt component for rendering interactive forms in chat
 */

import React, { useState } from 'react';
import { Send, X } from 'lucide-react';
import { FormRequest, FormField } from '@/types';

interface FormPromptProps {
  formRequest: FormRequest;
  onSubmit: (formData: Record<string, string>) => void;
  onCancel?: () => void;
  className?: string;
}

const FormPrompt: React.FC<FormPromptProps> = ({
  formRequest,
  onSubmit,
  onCancel,
  className = ''
}) => {
  const [formData, setFormData] = useState<Record<string, string>>(() => {
    // Initialize form data with default values
    const initialData: Record<string, string> = {};
    formRequest.fields.forEach(field => {
      initialData[field.name] = field.default_value || '';
    });
    return initialData;
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  const handleInputChange = (fieldName: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [fieldName]: value
    }));

    // Clear error when user starts typing
    if (errors[fieldName]) {
      setErrors(prev => ({
        ...prev,
        [fieldName]: ''
      }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    formRequest.fields.forEach(field => {
      if (field.required && !formData[field.name]?.trim()) {
        const label = field.name.replace(/_/g, ' ');
        newErrors[field.name] = `${label} is required`;
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (validateForm()) {
      onSubmit(formData);
    }
  };

  const renderField = (field: FormField) => {
    const hasError = !!errors[field.name];

    // Generate label from field name (replace underscores with spaces, keep original case)
    const label = field.name.replace(/_/g, ' ');

    // Generate placeholder from field name (replace underscores with spaces, keep original case)
    const placeholderText = field.name.replace(/_/g, ' ');
    const placeholder = `Enter your ${placeholderText}`;

    // Detect password fields
    const isPasswordField = field.name.toLowerCase().includes('password') ||
                           field.name.toLowerCase().includes('pass') ||
                           field.name.toLowerCase().includes('hash');
    const inputType = isPasswordField ? 'password' : 'text';

    return (
      <div key={field.name} className="mb-4">
        <label
          htmlFor={field.name}
          className="block text-sm font-medium text-theme-secondary mb-1"
        >
          {label}
          {field.required && <span className="text-accent-error ml-1">*</span>}
        </label>

        <input
          type={inputType}
          id={field.name}
          name={field.name}
          value={formData[field.name] || ''}
          onChange={(e) => handleInputChange(field.name, e.target.value)}
          placeholder={placeholder}
          required={field.required}
          className={`
            w-full px-3 py-2 border rounded-md shadow-sm
            bg-theme-primary text-theme-primary
            placeholder-theme-tertiary
            focus:outline-none focus:ring-2 focus:ring-accent-primary focus:border-accent-primary
            transition-colors
            ${hasError
              ? 'border-accent-error focus:ring-accent-error focus:border-accent-error'
              : 'border-theme-border hover:border-theme-secondary'
            }
          `}
        />

        {hasError && (
          <p className="mt-1 text-sm text-accent-error font-medium">
            {errors[field.name]}
          </p>
        )}
      </div>
    );
  };

  return (
    <div className={`bg-theme-primary border border-theme-border rounded-lg p-4 shadow-sm ${className}`}>
      {/* Form Header */}
      <div className="flex items-center justify-between mb-4">
        <div>
          <h3 className="text-lg font-semibold text-theme-primary">
            {formRequest.title}
          </h3>
          {formRequest.description && (
            <p className="text-sm text-theme-secondary mt-1">
              {formRequest.description}
            </p>
          )}
        </div>

        {onCancel && (
          <button
            onClick={onCancel}
            className="text-theme-tertiary hover:text-theme-secondary transition-colors"
            title="Cancel form"
          >
            <X size={20} />
          </button>
        )}
      </div>

      {/* Form Fields */}
      <form onSubmit={handleSubmit}>
        {formRequest.fields.map(renderField)}

        {/* Form Actions */}
        <div className="flex items-center justify-end gap-2 mt-6 pt-4 border-t border-theme-border">
          {onCancel && (
            <button
              type="button"
              onClick={onCancel}
              className="px-4 py-2 text-sm font-medium text-theme-secondary bg-theme-secondary hover:bg-theme-tertiary border border-theme-border rounded-md transition-colors"
            >
              Cancel
            </button>
          )}

          <button
            type="submit"
            className="flex items-center gap-2 px-4 py-2 text-sm font-medium text-white bg-accent-primary hover:bg-accent-primary-dark rounded-md transition-colors shadow-sm"
          >
            <Send size={16} />
            Submit
          </button>
        </div>
      </form>
    </div>
  );
};

export default FormPrompt;
