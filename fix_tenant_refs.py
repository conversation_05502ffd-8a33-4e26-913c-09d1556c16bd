#!/usr/bin/env python3
"""
Script to remove all tenant_id references from task_manager.py
"""

def fix_task_manager():
    """Remove all tenant_id references from task_manager.py"""
    file_path = "backend/task_manager.py"
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Remove all lines containing tenant_id=user_context.tenant_id
    lines = content.split('\n')
    new_lines = []
    
    for line in lines:
        if 'tenant_id=user_context.tenant_id' in line:
            # Skip this line entirely
            continue
        new_lines.append(line)
    
    # Join the lines back together
    new_content = '\n'.join(new_lines)
    
    # Write back to file
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(new_content)
    
    print(f"Removed all tenant_id references from {file_path}")

if __name__ == "__main__":
    fix_task_manager()
