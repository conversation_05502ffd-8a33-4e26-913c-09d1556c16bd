/**
 * ConfirmationModal component for general confirmation dialogs
 * Mobile-friendly and theme-aware modal component
 */

import React from 'react';
import { AlertTriangle } from 'lucide-react';
import { motion } from 'framer-motion';

interface ConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  confirmButtonStyle?: 'danger' | 'primary' | 'warning';
}

const ConfirmationModal: React.FC<ConfirmationModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  title,
  message,
  confirmText = "Yes",
  cancelText = "Cancel",
  confirmButtonStyle = "primary"
}) => {
  if (!isOpen) return null;

  const getConfirmButtonClasses = () => {
    switch (confirmButtonStyle) {
      case 'danger':
        return 'bg-accent-error text-white hover:opacity-90';
      case 'warning':
        return 'bg-accent-warning text-white hover:opacity-90';
      case 'primary':
      default:
        return 'bg-accent-primary text-white hover:opacity-90';
    }
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/60 backdrop-blur-sm animate-fade-in">
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.95 }}
        transition={{ duration: 0.2 }}
        className="bg-surface-primary border border-theme-primary rounded-xl shadow-2xl w-full max-w-sm mx-4 overflow-hidden"
      >
        {/* Header */}
        <div className="flex items-center gap-3 p-4 border-b border-theme-primary bg-surface-secondary">
          <AlertTriangle size={20} className="text-accent-warning flex-shrink-0" />
          <h3 className="font-semibold text-theme-primary flex-1">{title}</h3>
        </div>

        {/* Content */}
        <div className="p-4">
          <p className="text-theme-secondary text-sm leading-relaxed">{message}</p>
        </div>

        {/* Actions */}
        <div className="flex items-center justify-end gap-2 p-4 border-t border-theme-primary bg-surface-secondary">
          <motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={onClose}
            className="px-4 py-2 text-sm font-medium bg-surface-primary text-theme-secondary border border-theme-primary rounded-lg hover:bg-surface-secondary transition-colors touch-manipulation"
          >
            {cancelText}
          </motion.button>
          <motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={onConfirm}
            className={`px-4 py-2 text-sm font-medium rounded-lg transition-all touch-manipulation ${getConfirmButtonClasses()}`}
          >
            {confirmText}
          </motion.button>
        </div>
      </motion.div>
    </div>
  );
};

export default ConfirmationModal;
