/**
 * Backend Status Component
 * Provides diagnostics and status information for the backend connection
 */

import React, { useState, useEffect } from 'react';
import { AlertTriangle, CheckCircle, XCircle, RefreshCw, Server, Wifi, Clock, HelpCircle } from 'lucide-react';
import { apiService } from '@/services/apiService';
import TroubleshootingGuide from '@/components/TroubleshootingGuide';

interface BackendStatusProps {
  onStatusChange?: (isOnline: boolean) => void;
  autoRefresh?: boolean;
  refreshInterval?: number;
}

interface BackendStatusData {
  isOnline: boolean;
  health: any;
  connection: { success: boolean; message: string; latency?: number; details?: any };
  endpoints?: { [key: string]: boolean };
  recommendations: string[];
}

export const BackendStatus: React.FC<BackendStatusProps> = ({
  onStatusChange,
  autoRefresh = true,
  refreshInterval = 30000 // 30 seconds
}) => {
  const [status, setStatus] = useState<BackendStatusData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [lastChecked, setLastChecked] = useState<Date | null>(null);
  const [mounted, setMounted] = useState(false);
  const [showTroubleshooting, setShowTroubleshooting] = useState(false);

  const checkStatus = async () => {
    setIsLoading(true);
    try {
      const statusData = await apiService.getBackendStatus();
      setStatus(statusData);
      setLastChecked(new Date());
      onStatusChange?.(statusData.isOnline);
    } catch (error) {
      console.error('Failed to check backend status:', error);
      setStatus({
        isOnline: false,
        health: null,
        connection: { success: false, message: 'Failed to check status' },
        endpoints: {},
        recommendations: ['Unable to perform status check. Please verify your network connection.']
      });
      onStatusChange?.(false);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    setMounted(true);
    checkStatus();
  }, []);

  useEffect(() => {
    if (autoRefresh && refreshInterval > 0) {
      const interval = setInterval(checkStatus, refreshInterval);
      return () => clearInterval(interval);
    }
  }, [autoRefresh, refreshInterval]);

  const getStatusIcon = () => {
    if (isLoading) {
      return <RefreshCw className="w-5 h-5 animate-spin text-blue-500" />;
    }

    if (!status) {
      return <XCircle className="w-5 h-5 text-gray-400" />;
    }

    if (status.isOnline && status.health) {
      return <CheckCircle className="w-5 h-5 text-green-500" />;
    } else if (status.isOnline) {
      return <AlertTriangle className="w-5 h-5 text-yellow-500" />;
    } else {
      return <XCircle className="w-5 h-5 text-red-500" />;
    }
  };

  const getStatusText = () => {
    if (isLoading) return 'Checking...';
    if (!status) return 'Unknown';
    if (status.isOnline && status.health) return 'Online';
    if (status.isOnline) return 'Issues Detected';
    return 'Offline';
  };

  const getStatusColor = () => {
    if (isLoading) return 'text-accent-primary';
    if (!status) return 'text-theme-quaternary';
    if (status.isOnline && status.health) return 'text-accent-secondary';
    if (status.isOnline) return 'text-accent-warning';
    return 'text-accent-error';
  };

  return (
    <div className="bg-theme-primary border border-theme-primary rounded-xl p-4 shadow-lg transition-all duration-300">
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-2">
          <Server className="w-5 h-5 text-theme-secondary" />
          <h3 className="text-lg font-semibold text-theme-primary">Backend Status</h3>
        </div>
        <button
          onClick={checkStatus}
          disabled={isLoading}
          className="p-2 text-theme-secondary hover:text-theme-primary hover:bg-surface-secondary rounded-lg transition-all duration-200"
          title="Refresh status"
        >
          <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
        </button>
      </div>

      <div className="space-y-3">
        {/* Overall Status */}
        <div className="flex items-center space-x-3">
          {getStatusIcon()}
          <span className={`font-medium ${getStatusColor()}`}>
            {getStatusText()}
          </span>
          {status?.connection.latency && (
            <div className="flex items-center space-x-1 text-sm text-theme-tertiary">
              <Clock className="w-4 h-4" />
              <span>{status.connection.latency}ms</span>
            </div>
          )}
        </div>

        {/* Connection Details */}
        {status && (
          <div className="space-y-2 text-sm">
            <div className="flex items-center space-x-2">
              <Wifi className="w-4 h-4 text-theme-tertiary" />
              <span className="text-theme-secondary">Connection:</span>
              <span className={status.connection.success ? 'text-accent-secondary' : 'text-accent-error'}>
                {status.connection.message}
              </span>
            </div>

            {/* Endpoint Status */}
            {status.endpoints && Object.keys(status.endpoints).length > 0 && (
              <div className="mt-2">
                <div className="text-theme-secondary font-medium mb-1">Endpoints:</div>
                <div className="space-y-1">
                  {Object.entries(status.endpoints).map(([endpoint, isWorking]) => (
                    <div key={endpoint} className="flex items-center space-x-2 ml-4">
                      <div className={`w-2 h-2 rounded-full ${isWorking ? 'bg-accent-secondary' : 'bg-accent-error'}`} />
                      <span className="text-theme-tertiary">{endpoint}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Recommendations */}
            {status.recommendations.length > 0 && (
              <div className="mt-3 p-3 bg-surface-secondary border border-theme-secondary rounded-lg">
                <div className="flex items-center space-x-2 mb-2">
                  <AlertTriangle className="w-4 h-4 text-accent-warning" />
                  <span className="text-theme-primary font-medium">Recommendations:</span>
                </div>
                <ul className="space-y-1 text-theme-secondary text-sm">
                  {status.recommendations.map((rec, index) => (
                    <li key={index} className="flex items-start space-x-2">
                      <span className="text-accent-warning mt-1">•</span>
                      <span>{rec}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        )}

        {/* Last Checked - only show after component is mounted to prevent hydration issues */}
        {mounted && lastChecked && (
          <div className="text-xs text-theme-quaternary mt-2">
            Last checked: {lastChecked.toLocaleTimeString()}
          </div>
        )}

        {/* Troubleshooting Section */}
        {(!status?.isOnline || status.recommendations.length > 0) && (
          <div className="mt-4 border-t border-theme-primary pt-3">
            <button
              onClick={() => setShowTroubleshooting(!showTroubleshooting)}
              className="flex items-center space-x-2 text-sm text-accent-primary hover:text-accent-primary hover:opacity-80 transition-all duration-200"
            >
              <HelpCircle className="w-4 h-4" />
              <span>{showTroubleshooting ? 'Hide' : 'Show'} Troubleshooting Guide</span>
            </button>

            {showTroubleshooting && (
              <div className="mt-3">
                <TroubleshootingGuide />
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default BackendStatus;
