/**
 * Enhanced chat header component for VPS Admin Chat
 */

import React from 'react';
import { Menu } from 'lucide-react';
import { motion } from 'framer-motion';

interface ChatHeaderProps {
  isTaskActive: boolean;
  taskId: string | null;
  showLeftSidebar?: boolean;
  onToggleLeftSidebar?: () => void;
  className?: string;
}

const ChatHeader: React.FC<ChatHeaderProps> = ({
  isTaskActive,
  taskId,
  showLeftSidebar = false,
  onToggleLeftSidebar,
  className = ""
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      className={`p-3 border-b border-theme-primary bg-theme-primary flex-shrink-0 flex items-center justify-between transition-colors duration-200 ${className}`}
    >
      {/* Left side - Task Status and Tablet Toggle */}
      <div className="flex items-center gap-3 flex-1">
        {/* Tablet Sidebar Toggle Button */}
        {onToggleLeftSidebar && (
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={onToggleLeftSidebar}
            className={`hidden md:flex lg:hidden p-2 rounded-lg transition-colors tablet-sidebar-toggle ${
              showLeftSidebar
                ? 'bg-accent-primary/10 text-accent-primary border border-accent-primary/20'
                : 'bg-surface-secondary text-theme-secondary hover:bg-surface-tertiary'
            }`}
            title="Toggle Sidebar"
          >
            <Menu size={16} />
          </motion.button>
        )}

        {isTaskActive && taskId && (
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-accent-primary rounded-full animate-pulse"></div>
            <span className="text-sm text-theme-secondary">Task Active</span>
            <span className="text-xs text-theme-tertiary">({taskId.substring(0,6)})</span>
          </div>
        )}
      </div>


    </motion.div>
  );
};

export default ChatHeader;
