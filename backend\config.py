"""
Configuration management for the new WebSocket-based VPS Admin backend.
Handles environment variables, security settings, and application configuration.
"""

import os
from typing import List, Optional
from pydantic import Field, field_validator
from pydantic_settings import BaseSettings
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class Settings(BaseSettings):
    """Application settings with validation and defaults."""
    
    # Application settings
    app_name: str = "VPS Admin WebSocket Backend"
    app_version: str = "3.0.0"
    debug: bool = False
    
    # Server settings
    host: str = "0.0.0.0"
    port: int = 8000
    reload: bool = False
    
    # Security settings
    secret_key: str = "your-secret-key-change-in-production"
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30
    
    # CORS settings
    allowed_origins: List[str] = [
        "http://localhost:3000",
        "http://127.0.0.1:3000",
        "http://localhost:3001",
        "http://127.0.0.1:3001"
    ]
    
    # AI settings
    gemini_api_key: Optional[str] = None
    gemini_model: str = "models/gemini-2.5-flash-preview-05-20"
    intent_model: str = "models/gemma-3-27b-it"
    flash_model: str = "gemini-2.5-flash-preview-05-20"
    use_llm_intent: bool = True
    use_thinking: bool = True
    min_thinking_percentage: int = 50
    
    # VPS/SSH settings
    vps_hostname: str = "localhost"
    vps_port: int = 4646
    vps_username: str = "root"
    vps_password: Optional[str] = None
    ssh_private_key_path: Optional[str] = None
    ssh_timeout: int = 30
    
    # Redis settings (for session management and caching)
    redis_url: str = "redis://localhost:6379"
    redis_password: Optional[str] = None
    redis_db: int = 0
    
    # Rate limiting
    rate_limit_requests: int = 100
    rate_limit_window: int = 60  # seconds
    
    # WebSocket settings
    websocket_timeout: int = 300  # 5 minutes
    max_connections_per_user: int = 5
    heartbeat_interval: int = 30  # seconds
    
    # Task management
    max_concurrent_tasks: int = 10
    task_timeout: int = 3600  # 1 hour
    cleanup_interval: int = 300  # 5 minutes
    
    # Logging
    log_level: str = "INFO"
    log_format: str = "json"
    

    
    @field_validator('gemini_api_key', mode='before')
    @classmethod
    def validate_gemini_api_key(cls, v):
        if not v:
            v = os.getenv('GEMINI_API_KEY')
        if not v:
            raise ValueError('GEMINI_API_KEY is required')
        return v

    @field_validator('secret_key', mode='before')
    @classmethod
    def validate_secret_key(cls, v):
        if v == "your-secret-key-change-in-production":
            env_key = os.getenv('SECRET_KEY')
            if env_key:
                return env_key
            # Generate a random key for development
            import secrets
            return secrets.token_urlsafe(32)
        return v
    
    model_config = {
        "env_file": ".env",
        "case_sensitive": False,
        "extra": "ignore"
    }

# Global settings instance
settings = Settings()

# Environment-specific configurations
def get_database_url() -> str:
    """Get database URL based on environment."""
    if settings.debug:
        return "sqlite:///./dev.db"
    return os.getenv("DATABASE_URL", "sqlite:///./prod.db")

def get_cors_origins() -> List[str]:
    """Get CORS origins based on environment."""
    if settings.debug:
        return settings.allowed_origins + [
            "http://localhost:*",
            "http://127.0.0.1:*",
            "*"
        ]
    return settings.allowed_origins

def is_production() -> bool:
    """Check if running in production environment."""
    return os.getenv("ENVIRONMENT", "development").lower() == "production"
