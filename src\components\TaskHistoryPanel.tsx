/**
 * TaskHistoryPanel component for task history and management
 */

import React, { useState, useEffect } from 'react';
import { History, Plus, Clock, CheckCircle, XCircle, Pause, Play, Trash2, MoreHorizontal } from 'lucide-react';
import { motion } from 'framer-motion';
import { TaskHistoryPanelProps } from '@/types';
import ConfirmationModal from './ConfirmationModal';

const TaskHistoryPanel: React.FC<TaskHistoryPanelProps> = ({
  tasks,
  selectedTaskId,
  onTaskSelect,
  onNewTask,
  onDeleteTask
}) => {
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [taskToDelete, setTaskToDelete] = useState<string | null>(null);
  const [contextMenuOpen, setContextMenuOpen] = useState<string | null>(null);

  // Close context menu when clicking outside
  useEffect(() => {
    const handleClickOutside = () => {
      setContextMenuOpen(null);
    };

    if (contextMenuOpen) {
      document.addEventListener('click', handleClickOutside);
      return () => document.removeEventListener('click', handleClickOutside);
    }
  }, [contextMenuOpen]);
  const getStatusIcon = (status: 'pending' | 'running' | 'completed' | 'failed' | 'paused') => {
    switch (status) {
      case 'completed':
        return <CheckCircle size={14} className="text-green-500" />;
      case 'failed':
        return <XCircle size={14} className="text-red-500" />;
      case 'running':
        return <Play size={14} className="text-blue-500" />;
      case 'paused':
        return <Pause size={14} className="text-yellow-500" />;
      case 'pending':
        return <Clock size={14} className="text-gray-500" />;
      default:
        return <Clock size={14} className="text-gray-500" />;
    }
  };

  const handleContextMenuClick = (e: React.MouseEvent, taskId: string) => {
    e.stopPropagation();
    setContextMenuOpen(contextMenuOpen === taskId ? null : taskId);
  };

  const handleDeleteClick = (e: React.MouseEvent, taskId: string) => {
    e.stopPropagation();
    setTaskToDelete(taskId);
    setDeleteModalOpen(true);
    setContextMenuOpen(null);
  };

  const handleDeleteConfirm = () => {
    if (taskToDelete) {
      onDeleteTask(taskToDelete);
      setDeleteModalOpen(false);
      setTaskToDelete(null);
    }
  };

  const handleDeleteCancel = () => {
    setDeleteModalOpen(false);
    setTaskToDelete(null);
  };

  const formatTime = (date?: Date) => {
    if (!date) return 'Unknown';
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) return `${days}d ago`;
    if (hours > 0) return `${hours}h ago`;
    if (minutes > 0) return `${minutes}m ago`;
    return 'Just now';
  };

  return (
    <div className="flex flex-col h-full">
      {/* Panel Header */}
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-sm font-semibold text-theme-primary flex items-center gap-2">
          <History size={16} className="text-accent-primary" />
          Task History
        </h3>
      </div>

      {/* Task List - Scrollable */}
      <div className="flex-1 overflow-y-auto overflow-x-hidden minimal-scrollbar">
        <div className="space-y-2">
          {tasks.length === 0 ? (
            <div className="text-center py-6 text-theme-tertiary">
              <History size={24} className="mx-auto mb-2 opacity-50" />
              <p className="text-sm">No tasks yet</p>
            </div>
          ) : (
            tasks.map((task) => (
              <motion.button
                key={task.id}
                whileHover={{ scale: 1.01 }}
                whileTap={{ scale: 0.99 }}
                onClick={() => onTaskSelect(task.id)}
                className={`
                  w-full p-3 rounded-lg border transition-all duration-200 text-left overflow-hidden
                  ${selectedTaskId === task.id
                    ? 'border-accent-primary bg-accent-primary/10'
                    : 'border-theme-primary hover:bg-surface-secondary hover:border-accent-primary/50'
                  }
                `}
              >
                <div className="space-y-2 min-w-0">
                  {/* Task Header */}
                  <div className="flex items-start justify-between gap-2 min-w-0">
                    <div className="min-w-0 flex-1">
                      <div className="font-medium text-theme-primary text-sm truncate">
                        {task.title}
                      </div>
                      <div className="text-xs text-theme-tertiary truncate">
                        {task.description}
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex items-center gap-2 flex-shrink-0 relative">
                      {/* Status Icon */}
                      {getStatusIcon(task.status)}

                      {/* Context Menu Button */}
                      <motion.button
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.9 }}
                        onClick={(e) => handleContextMenuClick(e, task.id)}
                        className="p-1 rounded-lg text-theme-tertiary hover:text-theme-secondary hover:bg-surface-secondary transition-colors"
                        title="More Options"
                      >
                        <MoreHorizontal size={12} />
                      </motion.button>

                      {/* Context Menu */}
                      {contextMenuOpen === task.id && (
                        <div className="absolute right-0 top-8 bg-surface-primary border border-theme-primary rounded-lg shadow-lg z-10 min-w-[120px]">
                          <button
                            onClick={(e) => handleDeleteClick(e, task.id)}
                            className="w-full px-3 py-2 text-left text-xs text-red-500 hover:bg-red-50 dark:hover:bg-red-950/20 rounded-lg transition-colors flex items-center gap-2"
                          >
                            <Trash2 size={12} />
                            Delete
                          </button>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Task Metadata */}
                  <div className="flex items-center justify-between text-xs text-theme-tertiary min-w-0">
                    <span className="truncate">{formatTime(task.startTime)}</span>
                    {task.totalCommands > 0 && (
                      <span className="flex-shrink-0 ml-2">{task.successfulCommands}/{task.totalCommands}</span>
                    )}
                  </div>
                </div>
              </motion.button>
            ))
          )}
        </div>
      </div>

      {/* New Task Button - Sticky Bottom */}
      <div className="mt-3 pt-3 border-t border-theme-primary">
        <motion.button
          whileHover={{ scale: 1.01 }}
          whileTap={{ scale: 0.99 }}
          onClick={onNewTask}
          className="w-full p-3 border border-dashed border-theme-primary hover:border-accent-primary rounded-lg transition-all duration-200 group hover:bg-surface-secondary"
        >
          <div className="flex items-center justify-center gap-2 text-theme-tertiary group-hover:text-accent-primary">
            <Plus size={16} />
            <span className="text-sm font-medium">New Task</span>
          </div>
        </motion.button>
      </div>

      {/* Delete Confirmation Modal */}
      <ConfirmationModal
        isOpen={deleteModalOpen}
        onClose={handleDeleteCancel}
        onConfirm={handleDeleteConfirm}
        title="Delete Task"
        message={`Are you sure you want to delete "${tasks.find(t => t.id === taskToDelete)?.title}"? This action cannot be undone.`}
        confirmText="Delete"
        cancelText="Cancel"
        confirmButtonStyle="danger"
      />
    </div>
  );
};

export default TaskHistoryPanel;
