/**
 * Message handling hook for VPS Admin Chat
 */

import { useState, useCallback } from 'react';
import { Message, UseMessageHandlerReturn, SSHInfo, MessageMetadata } from '@/types';
import { createTaskId } from '@/utils';
import { MAX_MESSAGES_DISPLAY } from '@/constants';

export const useMessageHandler = (): UseMessageHandlerReturn => {
  const [messages, setMessages] = useState<Message[]>([]);

  const addMessage = useCallback((
    sender: Message['sender'],
    type: Message['type'],
    content: React.ReactNode,
    rawContent?: string,
    sshInfo?: SSHInfo,
    metadata?: MessageMetadata
  ) => {
    const timestamp = new Date();

    const newMessage: Message = {
      id: createTaskId(),
      sender,
      type,
      content,
      rawContent,
      rawCommand: (type === 'command_request' || type === 'interactive_command_request') ? rawContent : undefined,
      sshInfo,
      timestamp,
      metadata
    };

    setMessages(prev => {
      const updated = [...prev, newMessage];
      // Limit messages to prevent memory issues
      if (updated.length > MAX_MESSAGES_DISPLAY) {
        return updated.slice(-MAX_MESSAGES_DISPLAY);
      }
      return updated;
    });
  }, []);



  return {
    // State
    messages,
    setMessages,

    // Core operations
    addMessage
  };
};
