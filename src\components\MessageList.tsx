/**
 * Message list component for VPS Admin Chat
 */

import React, { useRef, useEffect } from 'react';
import { Message } from '@/types';
import MessageBubble from '@/components/MessageBubble';
import LoadingIndicator from '@/components/LoadingIndicator';

interface MessageListProps {
  messages: Message[];
  showConfirmation: boolean;
  commandToConfirm: string | null;
  isTaskActive: boolean;
  isWaiting: boolean;
  isAwaitingAnswer: boolean;
  autoScroll: boolean;
  onConfirmation: (confirm: 'yes' | 'no' | 'skip_step' | 'retry_step' | 'abort_task' | 'auto' | 'manual') => void;
  onFormSubmit?: (formData: Record<string, string>) => void;
  className?: string;
}

const MessageList: React.FC<MessageListProps> = ({
  messages,
  showConfirmation,
  commandToConfirm,
  isTaskActive,
  isWaiting,
  isAwaitingAnswer,
  autoScroll,
  onConfirmation,
  onFormSubmit,
  className = ""
}) => {
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Scroll to bottom when messages change (only if autoScroll is enabled)
  const scrollToBottom = () => {
    if (autoScroll) {
      messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
    }
  };

  useEffect(scrollToBottom, [messages, autoScroll]);

  return (
    <div className={`flex-1 overflow-y-auto p-2 sm:p-3 md:p-4 space-y-0 bg-theme-primary flex flex-col transition-colors duration-300 ${className}`}>
      {messages.map((message) => (
        <MessageBubble
          key={message.id}
          message={message}
          showConfirmation={showConfirmation}
          commandToConfirm={commandToConfirm}
          isTaskActive={isTaskActive}
          onConfirmation={onConfirmation}
          onFormSubmit={onFormSubmit}
        />
      ))}

      {/* Loading indicator - only show when task is active and waiting */}
      {isTaskActive && isWaiting && !showConfirmation && !isAwaitingAnswer && (
        <LoadingIndicator />
      )}

      {/* Scroll anchor */}
      <div ref={messagesEndRef} />
    </div>
  );
};

export default MessageList;
