# ---- Base Stage ----
# Use a specific version of Node.js for reproducibility.
# Alpine versions are smaller, which is great for CI/CD.
FROM node:18-alpine AS base
WORKDIR /app

# ---- Dependencies Stage ----
# This stage is only for installing dependencies.
# It's a separate stage to leverage <PERSON><PERSON>'s layer caching.
# This layer only gets rebuilt if package.json or package-lock.json changes.
FROM base AS deps
COPY package.json package-lock.json* ./
RUN npm ci

# ---- Builder Stage ----
# This stage builds the Next.js application.
# It copies the dependencies from the 'deps' stage and the source code.
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# Set build-time environment variables
ARG NEXT_PUBLIC_API_BASE_URL
ARG NEXT_PUBLIC_WEBSOCKET_URL
ARG NEXT_PUBLIC_HEALTH_CHECK_URL

ENV NEXT_PUBLIC_API_BASE_URL=$NEXT_PUBLIC_API_BASE_URL
ENV NEXT_PUBLIC_WEBSOCKET_URL=$NEXT_PUBLIC_WEBSOCKET_URL
ENV NEXT_PUBLIC_HEALTH_CHECK_URL=$NEXT_PUBLIC_HEALTH_CHECK_URL

RUN npm run build

# ---- Runner Stage (Final Image) ----
# This is the final, small, and optimized image.
# It starts from a fresh Node.js Alpine base to be as small as possible.
FROM base AS runner
WORKDIR /app

# Set environment to production
ENV NODE_ENV=production
# Uncomment the following line in case you want to disable telemetry during runtime.
# ENV NEXT_TELEMETRY_DISABLED 1

# Create a non-root user for security
RUN addgroup --system --gid 1001 nextjs
RUN adduser --system --uid 1001 nextjs

# Copy the standalone output from the builder stage.
# This includes the server.js file and the .next/static and .next/standalone directories.
COPY --from=builder --chown=nextjs:nextjs /app/public ./public
COPY --from=builder --chown=nextjs:nextjs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nextjs /app/.next/static ./.next/static

# Switch to the non-root user
USER nextjs

# Expose the port the app will run on
EXPOSE 3000

# Set the port environment variable
ENV PORT=3000

# The command to run the application.
# 'server.js' is created by the 'standalone' output mode.
CMD ["node", "server.js"]