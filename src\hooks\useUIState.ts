/**
 * UI State management hook for VPS Admin Chat
 */

import { useState, useCallback } from 'react';
import { UIState } from '@/types';

export const useUIState = () => {
  const [uiState, setUIState] = useState<UIState>({
    isWaiting: false,
    showConfirmation: false,
    commandToConfirm: null,
    error: null,
    isTaskActive: false,
    isAwaitingAnswer: false,
    showSettingsPanel: false,
    autoScroll: true
  });

  const updateUIState = useCallback((updates: Partial<UIState>) => {
    setUIState(prev => ({ ...prev, ...updates }));
  }, []);

  const setIsWaiting = useCallback((isWaiting: boolean) => {
    updateUIState({ isWaiting });
  }, [updateUIState]);

  const setShowConfirmation = useCallback((showConfirmation: boolean) => {
    updateUIState({ showConfirmation });
  }, [updateUIState]);

  const setCommandToConfirm = useCallback((commandToConfirm: string | null) => {
    updateUIState({ commandToConfirm });
  }, [updateUIState]);

  const setError = useCallback((error: string | null) => {
    updateUIState({ error });
  }, [updateUIState]);

  const setIsTaskActive = useCallback((isTaskActive: boolean) => {
    updateUIState({ isTaskActive });
  }, [updateUIState]);

  const setIsAwaitingAnswer = useCallback((isAwaitingAnswer: boolean) => {
    updateUIState({ isAwaitingAnswer });
  }, [updateUIState]);

  const setShowSettingsPanel = useCallback((showSettingsPanel: boolean) => {
    updateUIState({ showSettingsPanel });
  }, [updateUIState]);

  const setAutoScroll = useCallback((autoScroll: boolean) => {
    updateUIState({ autoScroll });
  }, [updateUIState]);

  const resetUIState = useCallback(() => {
    setUIState({
      isWaiting: false,
      showConfirmation: false,
      commandToConfirm: null,
      error: null,
      isTaskActive: false,
      isAwaitingAnswer: false,
      showSettingsPanel: false,
      autoScroll: true
    });
  }, []);

  return {
    ...uiState,
    updateUIState,
    setIsWaiting,
    setShowConfirmation,
    setCommandToConfirm,
    setError,
    setIsTaskActive,
    setIsAwaitingAnswer,
    setShowSettingsPanel,
    setAutoScroll,
    resetUIState
  };
};
