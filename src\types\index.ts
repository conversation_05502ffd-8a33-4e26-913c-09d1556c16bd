/**
 * Type definitions for VPS Admin Chat application
 */

import React from 'react';

// Enhanced message structure with more types and metadata
export interface Message {
  id: string;
  sender: 'user' | 'ai' | 'system';
  type: 'text' | 'ai_response' | 'command_request' | 'interactive_command_request' | 'command_output' | 'question' | 'error' | 'info' | 'warning' | 'summary' | 'task_end' | 'task_start' | 'progress' | 'file_operation' | 'security_alert' | 'step_start' | 'step_complete' | 'task_complete' | 'form_request' | 'form_response';
  content: React.ReactNode; // Can be string or JSX for formatted output
  rawContent?: string; // Store raw text content for markdown processing
  rawCommand?: string; // Store raw command for confirmation buttons
  sshInfo?: SSHInfo; // For command_output
  formData?: FormRequest; // For form_request messages
  timestamp?: Date;
  metadata?: MessageMetadata;
}

// SSH command execution result
export interface SSHInfo {
  stdout: string;
  stderr: string;
  exit_status: number;
  success: boolean;
  command?: string;
  execution_time?: number;
}

// Form-related types
export interface FormField {
  name: string;
  required: boolean;
  default_value?: string;
}

export interface FormRequest {
  form_id: string;
  title: string;
  description?: string;
  fields: FormField[];
}

export interface FormResponse {
  form_id: string;
  data: Record<string, string>;
}

// Message metadata for enhanced features
export interface MessageMetadata {
  stepNumber?: number;
  totalSteps?: number;
  category?: string;
  priority?: 'low' | 'medium' | 'high' | 'critical';
  duration?: number;
  retryCount?: number;
  securityRisk?: boolean;
  options?: string[]; // Available options for confirmation
  recoverySteps?: any[]; // Recovery steps for error handling
  recoveryApproach?: string; // Recovery approach description
  recoveryStepNumber?: number; // Current recovery step number
  totalRecoverySteps?: number; // Total recovery steps
}

// Enhanced task structure
export interface Task {
  id: string;
  title: string;
  description: string;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'paused';
  progress: number;
  steps: TaskStep[];
  startTime?: Date;
  endTime?: Date;
  totalCommands: number;
  successfulCommands: number;
  failedCommands: number;
}

export interface TaskStep {
  id: string;
  description: string;
  command?: string;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'skipped';
  result?: any;
  duration?: number;
  retryCount?: number;
}

// Execution statistics
export interface ExecutionStats {
  totalCommands: number;
  successfulCommands: number;
  failedCommands: number;
  averageExecutionTime: number;
  uptime: number;
  lastCommandTime?: Date | null;
}

// API request/response types
export interface StartTaskRequest {
  initial_prompt: string;
  task_metadata?: {
    title?: string;
    description?: string;
    created_at?: string;
    priority?: string;
    category?: string;
  };
}

export interface StartTaskResponse {
  task_id: string;
}

export interface SendMessageRequest {
  task_id: string;
  message: string;
}

// SSE event data types
export interface SSEEventData {
  type: string;
  content?: any;
  data?: any;
  metadata?: MessageMetadata;
  command?: string;
  timestamp?: string;
  task_id?: string;
}

// UI State types
export interface UIState {
  isWaiting: boolean;
  showConfirmation: boolean;
  commandToConfirm: string | null;
  error: string | null;
  isTaskActive: boolean;
  isAwaitingAnswer: boolean;
  showSettingsPanel: boolean;
  autoScroll: boolean;
}

// Command history state
export interface CommandHistoryState {
  commandHistory: string[];
  historyIndex: number;
}

// Export data structure
export interface ExportData {
  taskHistory: Task[];
  executionStats: ExecutionStats;
  exportDate: string;
  messages?: Message[];
}

// Component props types
export interface MessageBubbleProps {
  message: Message;
  showConfirmation: boolean;
  commandToConfirm: string | null;
  isTaskActive: boolean;
  onConfirmation: (confirm: 'yes' | 'no' | 'skip_step' | 'retry_step' | 'abort_task' | 'auto' | 'manual') => void;
  onFormSubmit?: (formData: Record<string, string>) => void;
}

export interface ConfirmationButtonsProps {
  onConfirm: (confirm: 'yes' | 'no' | 'skip_step' | 'retry_step' | 'abort_task' | 'auto' | 'manual') => void;
  isVisible: boolean;
  options?: string[]; // Available options for this confirmation
  showAdvancedOptions?: boolean; // Whether to show skip/retry/abort options
  isInteractiveCommand?: boolean; // Whether this is an interactive command (auto/manual)
}

export interface StatsPanelProps {
  executionStats: ExecutionStats;
  currentTask: Task | null;
  taskHistory: Task[];
  messages: Message[];
  autoScroll: boolean;
  onClose: () => void;
  onExportHistory: () => void;
  onToggleAutoScroll: () => void;
}

export interface SettingsPanelProps {
  executionStats: ExecutionStats;
  currentTask: Task | null;
  taskHistory: Task[];
  messages: Message[];
  autoScroll: boolean;
  onClose: () => void;
  onExportHistory: () => void;
  onToggleAutoScroll: () => void;
  onLogoutRequest: () => void;
  wsConnected?: boolean;
}

export interface ChatInputProps {
  value: string;
  onChange: (value: string) => void;
  onSubmit: () => void;
  onKeyDown: (event: React.KeyboardEvent<HTMLInputElement>) => void;
  disabled: boolean;
  placeholder: string;
  commandHistory: string[];
  historyIndex: number;
}

export interface ProgressIndicatorProps {
  task: Task;
  className?: string;
}

export interface MarkdownRendererProps {
  content: string | React.ReactNode;
  className?: string;
}

// Server management types
export interface Server {
  id: string;
  name: string;
  host: string;
  status: 'connected' | 'disconnected';
  icon?: string;
  lastConnected?: Date;
}

// Sidebar component types
export interface LeftSidebarProps {
  isOpen: boolean;
  onToggle: () => void;
  servers: Server[];
  taskHistory: Task[];
  currentServerId?: string;
  selectedTaskId?: string;
  onServerSelect: (serverId: string) => void;
  onAddServer: () => void;
  onNewTask: () => void;
  onTaskSelect: (taskId: string) => void;
  onDeleteServer: (serverId: string) => void;
  onDeleteTask: (taskId: string) => void;
  onDeleteAllTasks: () => void;
  onDeleteAccount: () => void;
  // Settings panel props
  executionStats: ExecutionStats;
  currentTask: Task | null;
  messages: Message[];
  autoScroll: boolean;
  onExportHistory: () => void;
  onToggleAutoScroll: () => void;
  onLogoutRequest: () => void;
  wsConnected?: boolean;
}

export interface ServerPanelProps {
  servers: Server[];
  currentServerId?: string;
  onServerSelect: (serverId: string) => void;
  onAddServer: () => void;
  onDeleteServer: (serverId: string) => void;
}

export interface TaskHistoryPanelProps {
  tasks: Task[];
  selectedTaskId?: string;
  onTaskSelect: (taskId: string) => void;
  onNewTask: () => void;
  onDeleteTask: (taskId: string) => void;
}

// Hook return types
export interface UseTaskManagerReturn {
  currentTask: Task | null;
  taskHistory: Task[];
  taskId: string | null;
  isTaskActive: boolean;
  createTask: (title: string, description: string) => Task;
  updateTaskProgress: (taskId: string, progress: number) => void;
  setCurrentTask: (task: Task | null) => void;
  setTaskHistory: (history: Task[]) => void;
  setTaskId: (id: string | null) => void;
  setIsTaskActive: (active: boolean) => void;
}

export interface UseMessageHandlerReturn {
  messages: Message[];
  addMessage: (
    sender: Message['sender'],
    type: Message['type'],
    content: React.ReactNode,
    rawContent?: string,
    sshInfo?: SSHInfo,
    metadata?: MessageMetadata
  ) => void;
  setMessages: React.Dispatch<React.SetStateAction<Message[]>>;
}

export interface UseStreamHandlerReturn {
  handleStream: (taskId: string, message: string) => Promise<void>;
  abortStream: () => void;
  isStreamActive: () => boolean;
  getStreamState?: () => string;
}

export interface UseCommandHistoryReturn {
  commandHistory: string[];
  historyIndex: number;
  addToCommandHistory: (command: string) => void;
  setHistoryIndex: (index: number) => void;
  navigateHistory: (direction: 'up' | 'down') => string;
  clearHistory: () => void;
}

export interface UseStatsReturn {
  executionStats: ExecutionStats;
  updateStats: (type: 'command_success' | 'command_failure', duration?: number) => void;
  resetStats: () => void;
}

// Credits system types
export interface Credits {
  current: number;
  total_purchased: number;
  last_updated: Date;
}

// Event handler types
export type ConfirmationHandler = (confirm: 'yes' | 'no' | 'skip_step' | 'retry_step' | 'abort_task' | 'auto' | 'manual') => void;
export type MessageSubmitHandler = (message: string) => void;
export type TaskStartHandler = (prompt: string) => Promise<void>;
export type KeyDownHandler = (event: React.KeyboardEvent<HTMLInputElement>) => void;
