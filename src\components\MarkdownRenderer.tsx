/**
 * MarkdownRenderer component for rendering markdown content
 */

import React from 'react';
import ReactMarkdown from 'react-markdown';
import { MarkdownRendererProps } from '@/types';
import { preprocessContentForMarkdown } from '@/utils';

const MarkdownRenderer: React.FC<MarkdownRendererProps> = ({
  content,
  className = "max-w-full overflow-hidden"
}) => {
  // Handle non-string content gracefully
  if (!content) {
    return <div className={`markdown-content ${className}`}></div>;
  }

  // If content is not a string, convert it to string or return as-is if it's a React element
  if (typeof content !== 'string') {
    // If it's a React element, render it directly
    if (React.isValidElement(content)) {
      return <div className={`markdown-content ${className}`}>{content}</div>;
    }
    // If it's an object, check if it's a system response that shouldn't be displayed
    if (typeof content === 'object') {
      // Filter out WebSocketResponse objects
      if (content !== null && 'success' in content && 'message' in content && 'error_code' in content) {
        console.log("MarkdownRenderer: Filtering out WebSocketResponse object:", content);
        return (
          <div className={`markdown-content ${className}`}>
            <span className="text-theme-secondary italic">[System response filtered]</span>
          </div>
        );
      }

      try {
        const stringContent = JSON.stringify(content);
        return (
          <div className={`markdown-content ${className}`}>
            <pre className="bg-theme-tertiary text-theme-primary p-2 rounded text-sm font-mono border border-theme-primary">
              {stringContent}
            </pre>
          </div>
        );
      } catch (error) {
        return (
          <div className={`markdown-content ${className}`}>
            <span className="text-theme-secondary italic">[Invalid content]</span>
          </div>
        );
      }
    }
    // For other types, convert to string
    content = String(content);
  }

  // Preprocess content to convert literal escape sequences to actual characters
  // At this point, content is guaranteed to be a string due to the checks above
  const processedContent = preprocessContentForMarkdown(content as string);

  return (
    <div className={`markdown-content ${className}`}>
      <ReactMarkdown
        components={{
          // Custom styling for markdown elements
          p: ({ children }) => <p className="mb-2 last:mb-0 break-words text-theme-primary">{children}</p>,
          strong: ({ children }) => <strong className="font-bold text-theme-primary">{children}</strong>,
          em: ({ children }) => <em className="italic text-theme-primary">{children}</em>,
          code: ({ children }) => <code className="bg-theme-tertiary text-theme-primary px-1 py-0.5 rounded text-sm font-mono border border-theme-primary inline-block mr-1">{children}</code>,
          pre: ({ children }) => <pre className="bg-theme-tertiary text-theme-primary p-2 rounded mt-2 mb-2 overflow-x-auto max-w-full border border-theme-primary">{children}</pre>,
          ul: ({ children }) => <ul className="list-disc list-inside mb-2 space-y-1 text-theme-primary">{children}</ul>,
          ol: ({ children }) => <ol className="list-decimal list-inside mb-2 space-y-1 text-theme-primary">{children}</ol>,
          li: ({ children }) => <li className="ml-2 break-words leading-relaxed text-theme-primary">{children}</li>,
          h1: ({ children }) => <h1 className="text-lg font-bold mb-2 break-words text-theme-primary">{children}</h1>,
          h2: ({ children }) => <h2 className="text-base font-bold mb-2 break-words text-theme-primary">{children}</h2>,
          h3: ({ children }) => <h3 className="text-sm font-bold mb-1 break-words text-theme-primary">{children}</h3>,
          blockquote: ({ children }) => <blockquote className="border-l-2 border-current/30 pl-3 italic mb-2 break-words text-theme-secondary">{children}</blockquote>,
        }}
      >
        {processedContent}
      </ReactMarkdown>
    </div>
  );
};

export default MarkdownRenderer;
