"""
WebSocket connection manager for real-time communication.
Handles connection lifecycle, message routing, and concurrent user management.
"""

import asyncio
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Set
from fastapi import WebSocket, WebSocketDisconnect
from pydantic import Validation<PERSON>rror
import structlog

from models import WebSocketMessage, WebSocketCommand, WebSocketResponse, MessageType, UserContext
from config import settings

logger = structlog.get_logger(__name__)

class ConnectionInfo:
    """Information about a WebSocket connection."""
    
    def __init__(self, websocket: WebSocket, user_id: str):
        self.websocket = websocket
        self.user_id = user_id
        self.connected_at = datetime.utcnow()
        self.last_heartbeat = datetime.utcnow()
        self.active_tasks: Set[str] = set()
        self.is_alive = True

    def update_heartbeat(self):
        """Update the last heartbeat timestamp."""
        self.last_heartbeat = datetime.utcnow()

    def add_task(self, task_id: str):
        """Add a task to this connection."""
        self.active_tasks.add(task_id)

    def remove_task(self, task_id: str):
        """Remove a task from this connection."""
        self.active_tasks.discard(task_id)

    def is_heartbeat_expired(self) -> bool:
        """Check if the connection heartbeat has expired."""
        return (datetime.utcnow() - self.last_heartbeat).total_seconds() > settings.websocket_timeout

class WebSocketManager:
    """Manages WebSocket connections and message routing."""
    
    def __init__(self):
        # Connection storage: connection_id -> ConnectionInfo
        self.connections: Dict[str, ConnectionInfo] = {}
        
        # User to connections mapping: user_id -> List[connection_id]
        self.user_connections: Dict[str, List[str]] = {}

        # Task to connections mapping: task_id -> List[connection_id]
        self.task_connections: Dict[str, List[str]] = {}
        
        # Lock for thread-safe operations
        self._lock = asyncio.Lock()
        
        # Start background tasks
        self._cleanup_task = None
        self._heartbeat_task = None

    async def connect(self, websocket: WebSocket, user_id: str) -> str:
        """Accept a new WebSocket connection."""
        await websocket.accept()

        # Generate unique connection ID
        connection_id = f"{user_id}_{datetime.utcnow().timestamp()}"

        async with self._lock:
            # Check connection limits per user
            user_connection_count = len(self.user_connections.get(user_id, []))
            if user_connection_count >= settings.max_connections_per_user:
                await websocket.close(code=1008, reason="Too many connections")
                raise Exception(f"User {user_id} has too many connections")

            # Create connection info
            connection_info = ConnectionInfo(websocket, user_id)
            self.connections[connection_id] = connection_info

            # Update mappings
            if user_id not in self.user_connections:
                self.user_connections[user_id] = []
            self.user_connections[user_id].append(connection_id)
        
        logger.info("WebSocket connected",
                   connection_id=connection_id,
                   user_id=user_id)
        
        # Start background tasks if not already running
        if self._cleanup_task is None:
            self._cleanup_task = asyncio.create_task(self._cleanup_connections())
        if self._heartbeat_task is None:
            self._heartbeat_task = asyncio.create_task(self._heartbeat_monitor())
        
        return connection_id

    async def disconnect(self, connection_id: str):
        """Disconnect a WebSocket connection."""
        async with self._lock:
            if connection_id not in self.connections:
                return
            
            connection_info = self.connections[connection_id]
            user_id = connection_info.user_id
            tenant_id = connection_info.tenant_id
            
            # Remove from all mappings
            del self.connections[connection_id]
            
            if user_id in self.user_connections:
                self.user_connections[user_id].remove(connection_id)
                if not self.user_connections[user_id]:
                    del self.user_connections[user_id]
            

            
            # Remove from task connections
            for task_id in connection_info.active_tasks:
                if task_id in self.task_connections:
                    if connection_id in self.task_connections[task_id]:
                        self.task_connections[task_id].remove(connection_id)
                    if not self.task_connections[task_id]:
                        del self.task_connections[task_id]
        
        logger.info("WebSocket disconnected", 
                   connection_id=connection_id, 
                   user_id=user_id, 
                   tenant_id=tenant_id)

    async def send_to_connection(self, connection_id: str, message: WebSocketMessage) -> bool:
        """Send a message to a specific connection."""
        if connection_id not in self.connections:
            return False
        
        connection_info = self.connections[connection_id]
        try:
            await connection_info.websocket.send_text(message.json())
            return True
        except Exception as e:
            logger.error("Failed to send message", 
                        connection_id=connection_id, 
                        error=str(e))
            await self.disconnect(connection_id)
            return False

    async def send_to_user(self, user_id: str, message: WebSocketMessage) -> int:
        """Send a message to all connections of a user."""
        sent_count = 0
        connection_ids = self.user_connections.get(user_id, []).copy()
        
        for connection_id in connection_ids:
            if await self.send_to_connection(connection_id, message):
                sent_count += 1
        
        return sent_count

    async def send_to_task(self, task_id: str, message: WebSocketMessage) -> int:
        """Send a message to all connections subscribed to a task."""
        sent_count = 0
        connection_ids = self.task_connections.get(task_id, []).copy()
        
        for connection_id in connection_ids:
            if await self.send_to_connection(connection_id, message):
                sent_count += 1
        
        return sent_count



    async def subscribe_to_task(self, connection_id: str, task_id: str):
        """Subscribe a connection to task updates."""
        if connection_id not in self.connections:
            return False
        
        async with self._lock:
            if task_id not in self.task_connections:
                self.task_connections[task_id] = []
            
            if connection_id not in self.task_connections[task_id]:
                self.task_connections[task_id].append(connection_id)
                self.connections[connection_id].add_task(task_id)
        
        return True

    async def unsubscribe_from_task(self, connection_id: str, task_id: str):
        """Unsubscribe a connection from task updates."""
        if connection_id not in self.connections:
            return False
        
        async with self._lock:
            if task_id in self.task_connections:
                if connection_id in self.task_connections[task_id]:
                    self.task_connections[task_id].remove(connection_id)
                if not self.task_connections[task_id]:
                    del self.task_connections[task_id]
            
            self.connections[connection_id].remove_task(task_id)
        
        return True

    async def handle_message(self, connection_id: str, message: str) -> WebSocketResponse:
        """Handle incoming WebSocket message."""
        try:
            command = WebSocketCommand.parse_raw(message)
            
            if command.action == "heartbeat":
                if connection_id in self.connections:
                    self.connections[connection_id].update_heartbeat()
                return WebSocketResponse(success=True, message="Heartbeat received")
            
            elif command.action == "subscribe_task":
                if command.task_id:
                    success = await self.subscribe_to_task(connection_id, command.task_id)
                    return WebSocketResponse(
                        success=success, 
                        message=f"Subscribed to task {command.task_id}" if success else "Failed to subscribe"
                    )
            
            elif command.action == "unsubscribe_task":
                if command.task_id:
                    success = await self.unsubscribe_from_task(connection_id, command.task_id)
                    return WebSocketResponse(
                        success=success, 
                        message=f"Unsubscribed from task {command.task_id}" if success else "Failed to unsubscribe"
                    )
            
            else:
                return WebSocketResponse(success=False, message="Unknown action", error_code="UNKNOWN_ACTION")
        
        except ValidationError as e:
            return WebSocketResponse(success=False, message="Invalid message format", error_code="VALIDATION_ERROR")
        except Exception as e:
            logger.error("Error handling WebSocket message", error=str(e))
            return WebSocketResponse(success=False, message="Internal error", error_code="INTERNAL_ERROR")

    async def get_connection_stats(self) -> Dict[str, int]:
        """Get connection statistics."""
        return {
            "total_connections": len(self.connections),
            "unique_users": len(self.user_connections),
            "active_tasks": len(self.task_connections)
        }

    async def _cleanup_connections(self):
        """Background task to clean up dead connections."""
        while True:
            try:
                await asyncio.sleep(settings.cleanup_interval)
                
                dead_connections = []
                async with self._lock:
                    for connection_id, connection_info in self.connections.items():
                        if connection_info.is_heartbeat_expired():
                            dead_connections.append(connection_id)
                
                for connection_id in dead_connections:
                    await self.disconnect(connection_id)
                    logger.info("Cleaned up dead connection", connection_id=connection_id)
                
            except Exception as e:
                logger.error("Error in connection cleanup", error=str(e))

    async def _heartbeat_monitor(self):
        """Background task to send heartbeat messages."""
        while True:
            try:
                await asyncio.sleep(settings.heartbeat_interval)
                
                heartbeat_message = WebSocketMessage(
                    type=MessageType.HEARTBEAT,
                    content={"timestamp": datetime.utcnow().isoformat()}
                )
                
                # Send heartbeat to all connections
                for connection_id in list(self.connections.keys()):
                    await self.send_to_connection(connection_id, heartbeat_message)
                
            except Exception as e:
                logger.error("Error in heartbeat monitor", error=str(e))

    async def close_all_connections(self):
        """Close all WebSocket connections gracefully."""
        logger.info("Closing all WebSocket connections")

        async with self._lock:
            connection_ids = list(self.connections.keys())

        for connection_id in connection_ids:
            try:
                await self.disconnect(connection_id)
                logger.debug("Closed connection", connection_id=connection_id)
            except Exception as e:
                logger.warning("Error closing connection",
                             connection_id=connection_id,
                             error=str(e))

        # Cancel background tasks
        if self._cleanup_task and not self._cleanup_task.done():
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass

        if self._heartbeat_task and not self._heartbeat_task.done():
            self._heartbeat_task.cancel()
            try:
                await self._heartbeat_task
            except asyncio.CancelledError:
                pass

        logger.info("All WebSocket connections closed")

# Global WebSocket manager instance
websocket_manager = WebSocketManager()
