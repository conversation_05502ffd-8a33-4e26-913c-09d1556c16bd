{"name": "vps-admin-nextjs", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "build:production": "cross-env NODE_ENV=production next build", "start": "next start", "start:production": "cross-env NODE_ENV=production next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "analyze": "ANALYZE=true next build", "export": "next export", "clean": "rimraf .next out", "preview": "npm run build && npm run start", "verify": "node scripts/verify-build.js", "deploy": "npm run type-check && npm run lint && npm run build:production && npm run verify"}, "dependencies": {"@microsoft/fetch-event-source": "^2.0.1", "@types/react-syntax-highlighter": "^15.5.13", "framer-motion": "^12.17.0", "lucide-react": "^0.514.0", "next": "15.3.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-markdown": "^10.1.0", "react-syntax-highlighter": "^15.6.1", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "cross-env": "^7.0.3", "eslint": "^9", "eslint-config-next": "15.3.3", "rimraf": "^6.0.1", "tailwindcss": "^4", "typescript": "^5"}}